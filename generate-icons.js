const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');

// Create icons directory if it doesn't exist
const iconsDir = './icons';
if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir);
    console.log('📁 Created icons directory');
}

// Icon sizes needed for PWA
const sizes = [72, 96, 128, 144, 152, 192, 384, 512];

function createIcon(size) {
    const canvas = createCanvas(size, size);
    const ctx = canvas.getContext('2d');
    
    // Create gradient background
    const gradient = ctx.createLinearGradient(0, 0, size, size);
    gradient.addColorStop(0, '#3b82f6');
    gradient.addColorStop(1, '#2563eb');
    
    // Draw rounded rectangle background
    const radius = size * 0.15;
    ctx.fillStyle = gradient;
    ctx.beginPath();
    
    // Manual rounded rectangle (since roundRect might not be available)
    ctx.moveTo(radius, 0);
    ctx.lineTo(size - radius, 0);
    ctx.quadraticCurveTo(size, 0, size, radius);
    ctx.lineTo(size, size - radius);
    ctx.quadraticCurveTo(size, size, size - radius, size);
    ctx.lineTo(radius, size);
    ctx.quadraticCurveTo(0, size, 0, size - radius);
    ctx.lineTo(0, radius);
    ctx.quadraticCurveTo(0, 0, radius, 0);
    ctx.closePath();
    ctx.fill();
    
    // Draw chef hat
    ctx.fillStyle = '#ffffff';
    const centerX = size / 2;
    const centerY = size / 2;
    const hatSize = size * 0.4;
    
    // Hat base (band)
    ctx.fillRect(centerX - hatSize * 0.6, centerY + hatSize * 0.3, hatSize * 1.2, hatSize * 0.2);
    
    // Hat top (puffy part)
    ctx.beginPath();
    ctx.ellipse(centerX, centerY - hatSize * 0.1, hatSize * 0.6, hatSize * 0.5, 0, 0, 2 * Math.PI);
    ctx.fill();
    
    // Hat details (small circles for texture)
    ctx.fillStyle = '#e2e8f0';
    for (let i = 0; i < 3; i++) {
        ctx.beginPath();
        ctx.arc(centerX - hatSize * 0.3 + i * hatSize * 0.3, centerY - hatSize * 0.1, size * 0.02, 0, 2 * Math.PI);
        ctx.fill();
    }
    
    // Add "RM" text below hat
    ctx.fillStyle = '#ffffff';
    ctx.font = `bold ${size * 0.15}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('RM', centerX, centerY + hatSize * 0.7);
    
    return canvas;
}

// Generate all icons
console.log('🎨 Generating RestroManager PWA icons...');

sizes.forEach(size => {
    try {
        const canvas = createIcon(size);
        const buffer = canvas.toBuffer('image/png');
        const filename = `icon-${size}x${size}.png`;
        const filepath = path.join(iconsDir, filename);
        
        fs.writeFileSync(filepath, buffer);
        console.log(`✅ Created ${filename}`);
    } catch (error) {
        console.error(`❌ Failed to create icon-${size}x${size}.png:`, error.message);
    }
});

console.log('\n🚀 All icons generated successfully!');
console.log('📁 Icons saved in ./icons/ directory');
console.log('🌐 You can now start your PWA server');
