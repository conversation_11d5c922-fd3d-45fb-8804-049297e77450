const http = require('http');
const https = require('https');
const fs = require('fs');
const path = require('path');
const url = require('url');

// MIME types for different file extensions
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'application/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.ico': 'image/x-icon',
  '.woff': 'font/woff',
  '.woff2': 'font/woff2'
};

class PWAServer {
  constructor(port = 8000, useHttps = false) {
    this.port = port;
    this.useHttps = useHttps;
  }

  // Get MIME type for file extension
  getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return mimeTypes[ext] || 'application/octet-stream';
  }

  // Handle HTTP requests
  handleRequest(req, res) {
    const parsedUrl = url.parse(req.url, true);
    let pathname = parsedUrl.pathname;

    // Handle root path
    if (pathname === '/') {
      pathname = '/index.html';
    }

    // Remove leading slash for file system
    const filePath = pathname.substring(1) || 'index.html';
    const fullPath = path.join(__dirname, filePath);

    // Set CORS headers for development
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    // Handle OPTIONS requests
    if (req.method === 'OPTIONS') {
      res.writeHead(200);
      res.end();
      return;
    }

    // Special handling for service worker
    if (pathname === '/sw.js') {
      res.setHeader('Content-Type', 'application/javascript');
      res.setHeader('Service-Worker-Allowed', '/');
      res.setHeader('Cache-Control', 'no-cache');
    }

    // Special handling for manifest
    if (pathname === '/manifest.json') {
      res.setHeader('Content-Type', 'application/manifest+json');
    }

    // Security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');

    // Check if file exists
    fs.access(fullPath, fs.constants.F_OK, (err) => {
      if (err) {
        // File not found
        res.writeHead(404, { 'Content-Type': 'text/html' });
        res.end(`
          <html>
            <head><title>404 - Not Found</title></head>
            <body>
              <h1>404 - File Not Found</h1>
              <p>The requested file <code>${pathname}</code> was not found.</p>
              <p><a href="/">Go back to home</a></p>
            </body>
          </html>
        `);
        return;
      }

      // Read and serve the file
      fs.readFile(fullPath, (err, data) => {
        if (err) {
          res.writeHead(500, { 'Content-Type': 'text/html' });
          res.end('<h1>500 - Internal Server Error</h1>');
          return;
        }

        const mimeType = this.getMimeType(fullPath);
        res.writeHead(200, { 'Content-Type': mimeType });
        res.end(data);
      });
    });

    // Log the request
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] ${req.method} ${pathname}`);
  }

  // Start the server
  start() {
    const server = http.createServer((req, res) => {
      this.handleRequest(req, res);
    });

    server.listen(this.port, () => {
      const protocol = this.useHttps ? 'https' : 'http';
      console.log('\n🚀 RestroManager PWA Server Started!');
      console.log('=====================================');
      console.log(`📱 Server running at ${protocol}://localhost:${this.port}`);
      console.log(`🌐 Access your PWA at ${protocol}://localhost:${this.port}`);
      console.log(`📋 Dashboard: ${protocol}://localhost:${this.port}/dashboard.html`);
      console.log(`✍️  Signup: ${protocol}://localhost:${this.port}/signup.html`);
      console.log('\n💡 Features:');
      console.log('   ✅ Service Worker support');
      console.log('   ✅ PWA manifest serving');
      console.log('   ✅ CORS headers for development');
      console.log('   ✅ Proper MIME types');
      console.log('\n🔧 Press Ctrl+C to stop the server\n');
    });

    // Handle server errors
    server.on('error', (err) => {
      if (err.code === 'EADDRINUSE') {
        console.error(`❌ Port ${this.port} is already in use`);
        console.log(`💡 Try a different port: node server.js ${this.port + 1}`);
      } else {
        console.error('❌ Server error:', err.message);
      }
      process.exit(1);
    });

    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n🛑 Server stopped by user');
      server.close(() => {
        console.log('✅ Server closed gracefully');
        process.exit(0);
      });
    });

    return server;
  }
}

// Command line interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const port = parseInt(args[0]) || 8000;
  const useHttps = args.includes('--https');

  // Check if icons directory exists
  if (!fs.existsSync('./icons')) {
    console.log('⚠️  Icons directory not found!');
    console.log('💡 Please create the icons directory and add PWA icons:');
    console.log('   1. Open generate-icons.html in your browser');
    console.log('   2. Download all icons');
    console.log('   3. Save them in the ./icons/ folder');
    console.log('');
  }

  // Check if required files exist
  const requiredFiles = ['manifest.json', 'sw.js', 'pwa-utils.js'];
  const missingFiles = requiredFiles.filter(file => !fs.existsSync(`./${file}`));
  
  if (missingFiles.length > 0) {
    console.log('❌ Missing required PWA files:');
    missingFiles.forEach(file => console.log(`   - ${file}`));
    console.log('');
  }

  const server = new PWAServer(port, useHttps);
  server.start();
}

module.exports = PWAServer;
