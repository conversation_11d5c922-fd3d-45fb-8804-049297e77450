<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RestroManager - Dashboard</title>

    <!-- PWA Meta Tags -->
    <meta name="description" content="Complete restaurant management solution for orders, menu, tasks, and analytics">
    <meta name="theme-color" content="#2563eb">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="RestroManager">
    <meta name="msapplication-TileColor" content="#2563eb">
    <meta name="msapplication-config" content="/browserconfig.xml">

    <!-- PWA Manifest -->
    <link rel="manifest" href="./manifest.json">

    <!-- PWA Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="./icons/icon-72x72.png">
    <link rel="icon" type="image/png" sizes="16x16" href="./icons/icon-72x72.png">
    <link rel="apple-touch-icon" sizes="180x180" href="./icons/icon-192x192.png">
    <link rel="mask-icon" href="./icons/icon-192x192.png" color="#2563eb">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest"></script>

    <!-- PWA Utils -->
    <script src="./pwa-utils.js"></script>

    <style>
        :root {
            --primary-50: #eff6ff;
            --primary-100: #dbeafe;
            --primary-500: #3b82f6;
            --primary-600: #2563eb;
            --primary-700: #1d4ed8;
            --success-50: #f0fdf4;
            --success-500: #22c55e;
            --warning-50: #fffbeb;
            --warning-500: #f59e0b;
            --error-50: #fef2f2;
            --error-500: #ef4444;
        }

        /* Custom base styles */
        body {
            font-family: 'Inter', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { background: #f1f5f9; }
        ::-webkit-scrollbar-thumb { background: #94a3b8; border-radius: 10px; }
        ::-webkit-scrollbar-thumb:hover { background: #64748b; }

        /* Glass morphism effects */
        .glass-card {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .stat-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        /* Enhanced sidebar styles */
        .sidebar-item {
            transition: all 0.2s ease;
            position: relative;
        }

        .sidebar-item:hover {
            background: rgba(59, 130, 246, 0.1);
            transform: translateX(4px);
        }

        .sidebar-item.active {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }

        .sidebar-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #ffffff;
            border-radius: 0 4px 4px 0;
        }

        /* Sidebar transition and base styles */
        #sidebar {
            transition: width 0.3s ease-in-out, transform 0.3s ease-in-out;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        /* Collapsed state for the sidebar */
        #sidebar.collapsed {
            width: 5rem; /* w-20 */
        }

        /* Hide text and adjust padding when collapsed */
        #sidebar.collapsed .sidebar-link-text,
        #sidebar.collapsed .logo-text {
            display: none;
        }
        #sidebar.collapsed .sidebar-link {
            justify-content: center;
        }
        #sidebar.collapsed #sidebar-toggle .lucide-chevrons-left {
            transform: rotate(180deg);
        }
        
        /* Tooltip for collapsed sidebar */
        .sidebar-link .tooltip {
            visibility: hidden;
            opacity: 0;
            transition: opacity 0.2s;
            position: absolute;
            left: 5.5rem; /* Position next to the icon */
            background-color: #1f2937;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            white-space: nowrap;
            z-index: 50;
        }

        #sidebar.collapsed .sidebar-link:hover .tooltip {
            visibility: visible;
            opacity: 1;
        }

        /* Active link styles */
        .sidebar-link.active {
            background-color: #eff6ff;
            color: #2563eb;
            font-weight: 600;
        }
        .sidebar-link.active svg { color: #2563eb; }

        /* Content section visibility */
        .content-section {
            display: none;
            width: 100%;
            position: relative;
            z-index: 1;
        }
        .content-section.active {
            display: block;
            width: 100%;
            position: relative;
            z-index: 1;
        }

        /* Ensure orders content is properly positioned */
        #orders-content {
            position: relative !important;
            top: auto !important;
            left: auto !important;
            right: auto !important;
            bottom: auto !important;
            margin-top: 0 !important;
            padding-top: 0 !important;
        }

        /* Active filter button style */
        .filter-btn.active {
            background-color: #2563eb;
            color: white;
        }
        
        /* Drag and Drop styles */
        .task-card.dragging {
            opacity: 0.5;
            transform: rotate(2deg);
        }
        .kanban-column .tasks-container.drag-over {
            border-style: dashed;
            border-color: #2563eb;
            background-color: #eff6ff;
        }

        /* Professional utility styles */
        .metric-trend {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 12px;
        }

        .trend-up {
            background: var(--success-50);
            color: var(--success-500);
        }

        .trend-down {
            background: var(--error-50);
            color: var(--error-500);
        }

        .trend-neutral {
            background: var(--warning-50);
            color: var(--warning-500);
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .status-active {
            background: var(--success-50);
            color: var(--success-500);
        }

        .status-pending {
            background: var(--warning-50);
            color: var(--warning-500);
        }

        .status-completed {
            background: var(--primary-50);
            color: var(--primary-600);
        }

        .quick-action {
            background: linear-gradient(135deg, #ffffff, #f8fafc);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .quick-action:hover {
            border-color: var(--primary-500);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
        }

        .table-row {
            transition: all 0.2s ease;
        }

        .table-row:hover {
            background: rgba(59, 130, 246, 0.05);
            transform: scale(1.01);
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-slide-in {
            animation: slideIn 0.5s ease-out;
        }

        /* Mobile responsive adjustments */
        @media (max-width: 768px) {
            #sidebar {
                transform: translateX(-100%);
                position: fixed;
                z-index: 50;
                height: 100vh;
            }
            #sidebar.open {
                transform: translateX(0);
            }
            #main-content {
                margin-left: 0;
            }
        }

    </style>
</head>
<body class="bg-slate-100">

    <div class="relative min-h-screen lg:flex">
        <!-- Backdrop for mobile sidebar -->
        <div id="sidebar-backdrop" class="fixed inset-0 bg-black bg-opacity-50 z-20 hidden"></div>

        <!-- Sidebar -->
        <aside id="sidebar" class="w-64 bg-white shadow-lg flex flex-col justify-between fixed inset-y-0 left-0 transform lg:relative z-30">
            <div>
                <!-- Logo -->
                <div class="h-20 flex items-center justify-center border-b px-4 overflow-hidden">
                    <a href="#" class="flex items-center gap-2">
                        <i data-lucide="chef-hat" class="w-8 h-8 text-blue-600 flex-shrink-0"></i>
                        <h1 class="text-2xl font-bold text-blue-600 logo-text whitespace-nowrap">Restro<span class="text-slate-800">Manager</span></h1>
                    </a>
                </div>
                
                <!-- Navigation Links -->
                <nav class="mt-6 space-y-1 px-3">
                    <a href="#" data-target="dashboard" class="sidebar-item sidebar-link active flex items-center py-3 px-4 rounded-xl text-slate-700">
                        <i data-lucide="layout-dashboard" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                        <span class="sidebar-link-text font-medium">Dashboard</span>
                    </a>
                    <a href="#" data-target="orders" class="sidebar-item sidebar-link flex items-center py-3 px-4 rounded-xl text-slate-700">
                        <i data-lucide="shopping-cart" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                        <span class="sidebar-link-text font-medium">Orders</span>
                        <span class="ml-auto bg-orange-100 text-orange-600 text-xs font-medium px-2 py-1 rounded-full">8</span>
                    </a>

                    <a href="#" data-target="tables" class="sidebar-item flex items-center py-3 px-4 rounded-xl text-slate-700">
                        <i data-lucide="layout" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                        <span class="sidebar-link-text font-medium">Tables</span>
                        <span class="ml-auto bg-green-100 text-green-600 text-xs font-medium px-2 py-1 rounded-full">12/16</span>
                    </a>
                    <a href="#" data-target="tasks" class="sidebar-item flex items-center py-3 px-4 rounded-xl text-slate-700">
                        <i data-lucide="clipboard-check" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                        <span class="sidebar-link-text font-medium">Tasks</span>
                        <span class="ml-auto bg-blue-100 text-blue-600 text-xs font-medium px-2 py-1 rounded-full">5</span>
                    </a>
                    <a href="#" data-target="customers" class="sidebar-item flex items-center py-3 px-4 rounded-xl text-slate-700">
                        <i data-lucide="users" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                        <span class="sidebar-link-text font-medium">Customers</span>
                    </a>
                    <a href="#" data-target="inventory" class="sidebar-item flex items-center py-3 px-4 rounded-xl text-slate-700">
                        <i data-lucide="package" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                        <span class="sidebar-link-text font-medium">Inventory</span>
                        <span class="ml-auto bg-orange-100 text-orange-600 text-xs font-medium px-2 py-1 rounded-full">!</span>
                    </a>
                    <a href="#" data-target="analytics" class="sidebar-item flex items-center py-3 px-4 rounded-xl text-slate-700">
                        <i data-lucide="bar-chart-2" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                        <span class="sidebar-link-text font-medium">Analytics</span>
                    </a>
                </nav>
            </div>
            
            <!-- Pro Plan Banner -->
            <div class="mt-auto px-3 mb-4">
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-100">
                    <div class="flex items-center gap-3 mb-2">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i data-lucide="zap" class="w-4 h-4 text-blue-600"></i>
                        </div>
                        <div>
                            <p class="text-sm font-semibold text-slate-800">Pro Plan</p>
                            <p class="text-xs text-slate-500">Upgrade for more features</p>
                        </div>
                    </div>
                    <button class="w-full bg-blue-600 text-white text-xs font-medium py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        Upgrade Now
                    </button>
                </div>
            </div>

            <!-- Sidebar Toggle and Settings -->
            <div class="mb-2 px-3">
                <a href="#" data-target="settings" class="sidebar-item flex items-center py-3 px-4 rounded-xl text-slate-700">
                    <i data-lucide="settings" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                    <span class="sidebar-link-text font-medium">Settings</span>
                </a>
                <button id="sidebar-toggle" class="hidden lg:flex items-center justify-center w-full py-3 text-slate-500 hover:bg-slate-100 rounded-lg mt-2">
                    <i data-lucide="chevrons-left" class="w-5 h-5 transition-transform duration-300"></i>
                </button>
            </div>
        </aside>

        <!-- Main content wrapper -->
        <div id="main-content-wrapper" class="flex-1 flex flex-col overflow-hidden min-h-screen">
            <!-- Header -->
            <header class="flex justify-between items-center p-4 sm:p-6 bg-white border-b">
                <div class="flex items-center">
                    <!-- Mobile Menu Button -->
                    <button id="mobile-menu-button" class="lg:hidden mr-4 text-slate-600 hover:text-slate-900">
                        <i data-lucide="menu" class="w-6 h-6"></i>
                    </button>
                    <h2 id="header-title" class="text-2xl font-semibold text-slate-800">Dashboard</h2>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- User Role Badge -->
                    <div id="user-role-badge" class="hidden lg:flex items-center gap-2 px-3 py-2 bg-blue-100 text-blue-800 rounded-lg text-sm font-medium">
                        <span id="role-icon">👨‍💼</span>
                        <span id="role-text">Admin</span>
                    </div>

                    <button class="text-slate-600 hover:text-blue-600"><i data-lucide="bell" class="w-6 h-6"></i></button>

                    <!-- User Menu -->
                    <div class="relative">
                        <button id="user-menu-btn" class="flex items-center gap-2 text-slate-600 hover:text-slate-800 transition-colors">
                            <img id="user-avatar" class="h-10 w-10 rounded-full object-cover" src="https://placehold.co/100x100/E2E8F0/475569?text=AV" alt="User avatar">
                            <span class="absolute right-0 bottom-0 h-3 w-3 bg-green-500 rounded-full border-2 border-white"></span>
                            <i data-lucide="chevron-down" class="w-4 h-4"></i>
                        </button>

                        <!-- Dropdown Menu -->
                        <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-slate-200 py-2 z-50">
                            <div class="px-4 py-2 border-b border-slate-100">
                                <p id="user-name" class="font-semibold text-slate-800">User Name</p>
                                <p id="user-email" class="text-sm text-slate-500"><EMAIL></p>
                            </div>
                            <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                <i data-lucide="user" class="w-4 h-4"></i>
                                Profile
                            </a>
                            <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                <i data-lucide="settings" class="w-4 h-4"></i>
                                Settings
                            </a>
                            <hr class="my-2">
                            <button id="logout-btn" class="flex items-center gap-2 w-full px-4 py-2 text-red-600 hover:bg-red-50 transition-colors">
                                <i data-lucide="log-out" class="w-4 h-4"></i>
                                Logout
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main content area -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gradient-to-br from-slate-50 to-blue-50 p-4 sm:p-6 lg:p-8 relative">
                <!-- Welcome Banner for New Users -->
                <div id="welcome-banner" class="hidden mb-6 bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6 rounded-xl shadow-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-xl font-bold mb-2">🎉 Welcome to RestroManager!</h3>
                            <p class="text-blue-100">Hi <span id="user-name"></span>! Your account has been successfully created. Let's get your restaurant set up!</p>
                        </div>
                        <button id="dismiss-welcome" class="text-blue-200 hover:text-white transition-colors">
                            <i data-lucide="x" class="w-6 h-6"></i>
                        </button>
                    </div>
                    <div class="mt-4 flex flex-wrap gap-3">
                        <button class="bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-blue-50 transition-colors">
                            Complete Setup
                        </button>
                        <button class="bg-blue-400 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-300 transition-colors">
                            Watch Tutorial
                        </button>
                    </div>
                </div>

                <!-- Dashboard Content -->
                <div id="dashboard-content" class="content-section active">
                    <!-- Key Metrics Cards -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
                        <!-- Today's Revenue -->
                        <div class="stat-card rounded-2xl p-6 shadow-lg animate-slide-in" style="animation-delay: 0.1s;">
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-slate-500">Today's Revenue</span>
                                    <span class="text-2xl font-bold text-slate-800">₹24,589</span>
                                </div>
                                <div class="p-2 bg-blue-50 rounded-lg">
                                    <i data-lucide="trending-up" class="w-6 h-6 text-blue-600"></i>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="metric-trend trend-up">
                                    <i data-lucide="arrow-up-right" class="w-3 h-3"></i>
                                    <span>12.5%</span>
                                </div>
                                <span class="text-xs text-slate-500">vs. yesterday</span>
                            </div>
                        </div>

                        <!-- Active Orders -->
                        <div class="stat-card rounded-2xl p-6 shadow-lg animate-slide-in" style="animation-delay: 0.2s;">
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-slate-500">Active Orders</span>
                                    <span class="text-2xl font-bold text-slate-800">18</span>
                                </div>
                                <div class="p-2 bg-green-50 rounded-lg">
                                    <i data-lucide="clipboard-list" class="w-6 h-6 text-green-600"></i>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="metric-trend trend-up">
                                    <i data-lucide="arrow-up-right" class="w-3 h-3"></i>
                                    <span>8.2%</span>
                                </div>
                                <span class="text-xs text-slate-500">vs. last hour</span>
                            </div>
                        </div>

                        <!-- Table Occupancy -->
                        <div class="stat-card rounded-2xl p-6 shadow-lg animate-slide-in" style="animation-delay: 0.3s;">
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-slate-500">Table Occupancy</span>
                                    <span class="text-2xl font-bold text-slate-800">76%</span>
                                </div>
                                <div class="p-2 bg-orange-50 rounded-lg">
                                    <i data-lucide="utensils" class="w-6 h-6 text-orange-600"></i>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="metric-trend trend-neutral">
                                    <i data-lucide="minus" class="w-3 h-3"></i>
                                    <span>2.1%</span>
                                </div>
                                <span class="text-xs text-slate-500">vs. yesterday</span>
                            </div>
                        </div>

                        <!-- Customer Satisfaction -->
                        <div class="stat-card rounded-2xl p-6 shadow-lg animate-slide-in" style="animation-delay: 0.4s;">
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-slate-500">Customer Rating</span>
                                    <span class="text-2xl font-bold text-slate-800">4.8/5</span>
                                </div>
                                <div class="p-2 bg-purple-50 rounded-lg">
                                    <i data-lucide="star" class="w-6 h-6 text-purple-600"></i>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="metric-trend trend-up">
                                    <i data-lucide="arrow-up-right" class="w-3 h-3"></i>
                                    <span>0.3</span>
                                </div>
                                <span class="text-xs text-slate-500">vs. last week</span>
                            </div>
                        </div>
                    </div>

                    <!-- Charts and Analytics Section -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                        <!-- Revenue Chart -->
                        <div class="lg:col-span-2 glass-card rounded-2xl p-6 shadow-lg animate-slide-in" style="animation-delay: 0.5s;">
                            <div class="flex justify-between items-center mb-6">
                                <div>
                                    <h3 class="text-lg font-semibold text-slate-800">Revenue Analytics</h3>
                                    <p class="text-sm text-slate-500">Last 7 days performance</p>
                                </div>
                                <div class="flex gap-2">
                                    <button class="px-3 py-1 text-xs font-medium bg-blue-100 text-blue-700 rounded-lg">7D</button>
                                    <button class="px-3 py-1 text-xs font-medium text-slate-500 hover:bg-slate-100 rounded-lg">30D</button>
                                    <button class="px-3 py-1 text-xs font-medium text-slate-500 hover:bg-slate-100 rounded-lg">90D</button>
                                </div>
                            </div>
                            <div class="h-64 flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl">
                                <div class="text-center">
                                    <i data-lucide="bar-chart-3" class="w-12 h-12 text-blue-400 mx-auto mb-2"></i>
                                    <p class="text-sm text-slate-500">Revenue chart will be displayed here</p>
                                </div>
                            </div>
                        </div>

                        <!-- Top Dishes -->
                        <div class="glass-card rounded-2xl p-6 shadow-lg animate-slide-in" style="animation-delay: 0.6s;">
                            <div class="flex justify-between items-center mb-6">
                                <h3 class="text-lg font-semibold text-slate-800">Top Dishes</h3>
                                <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View All</button>
                            </div>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                            <span class="text-lg">🍕</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-slate-800">Margherita Pizza</p>
                                            <p class="text-xs text-slate-500">42 orders</p>
                                        </div>
                                    </div>
                                    <span class="text-sm font-semibold text-green-600">₹1,680</span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <span class="text-lg">🍝</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-slate-800">Pasta Alfredo</p>
                                            <p class="text-xs text-slate-500">38 orders</p>
                                        </div>
                                    </div>
                                    <span class="text-sm font-semibold text-blue-600">₹1,520</span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                                            <span class="text-lg">🍔</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-slate-800">Classic Burger</p>
                                            <p class="text-xs text-slate-500">35 orders</p>
                                        </div>
                                    </div>
                                    <span class="text-sm font-semibold text-orange-600">₹1,400</span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-violet-50 rounded-xl">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                            <span class="text-lg">🥗</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-slate-800">Caesar Salad</p>
                                            <p class="text-xs text-slate-500">28 orders</p>
                                        </div>
                                    </div>
                                    <span class="text-sm font-semibold text-purple-600">₹1,120</span>
                                </div>
                            </div>
                        </div>
                    </div>


                        <div class="bg-white p-6 rounded-lg shadow-md flex items-center justify-between"><div><p class="text-sm font-medium text-slate-500">Today's Revenue</p><p class="text-3xl font-bold text-slate-800 mt-1">$1,250.60</p></div><div class="bg-blue-100 text-blue-600 p-3 rounded-full"><i data-lucide="dollar-sign" class="w-6 h-6"></i></div></div>
                        <div class="bg-white p-6 rounded-lg shadow-md flex items-center justify-between"><div><p class="text-sm font-medium text-slate-500">Today's Orders</p><p class="text-3xl font-bold text-slate-800 mt-1">102</p></div><div class="bg-green-100 text-green-600 p-3 rounded-full"><i data-lucide="shopping-cart" class="w-6 h-6"></i></div></div>
                        <div class="bg-white p-6 rounded-lg shadow-md flex items-center justify-between"><div><p class="text-sm font-medium text-slate-500">New Customers</p><p class="text-3xl font-bold text-slate-800 mt-1">12</p></div><div class="bg-indigo-100 text-indigo-600 p-3 rounded-full"><i data-lucide="user-plus" class="w-6 h-6"></i></div></div>
                        <div class="bg-white p-6 rounded-lg shadow-md flex items-center justify-between"><div><p class="text-sm font-medium text-slate-500">Pending Orders</p><p class="text-3xl font-bold text-slate-800 mt-1">5</p></div><div class="bg-orange-100 text-orange-600 p-3 rounded-full"><i data-lucide="loader" class="w-6 h-6"></i></div></div>
                    </div>

                </div>


                <!-- Orders Content -->
                <div id="orders-content" class="content-section">
                    <!-- Header -->
                    <header class="flex justify-between items-center p-4 sm:p-6 bg-white border-b">
                        <div class="flex items-center">
                            <!-- Mobile Menu Button -->
                            <button id="mobile-menu-button" class="lg:hidden mr-4 text-slate-600 hover:text-slate-900">
                                <i data-lucide="menu" class="w-6 h-6"></i>
                            </button>
                            <h2 id="header-title" class="text-2xl font-semibold text-slate-800">Orders Management</h2>
                        </div>
                        <div class="flex items-center space-x-4">
                            <!-- User Role Badge -->
                            <div id="user-role-badge" class="hidden lg:flex items-center gap-2 px-3 py-2 bg-blue-100 text-blue-800 rounded-lg text-sm font-medium">
                                <span id="role-icon">👨‍💼</span>
                                <span id="role-text">Admin</span>
                            </div>

                            <button class="text-slate-600 hover:text-blue-600"><i data-lucide="bell" class="w-6 h-6"></i></button>

                            <!-- User Menu -->
                            <div class="relative">
                                <button id="user-menu-btn" class="flex items-center gap-2 text-slate-600 hover:text-slate-800 transition-colors">
                                    <img id="user-avatar" class="h-10 w-10 rounded-full object-cover" src="https://placehold.co/100x100/E2E8F0/475569?text=AV" alt="User avatar">
                                    <span class="absolute right-0 bottom-0 h-3 w-3 bg-green-500 rounded-full border-2 border-white"></span>
                                    <i data-lucide="chevron-down" class="w-4 h-4"></i>
                                </button>

                                <!-- Dropdown Menu -->
                                <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-slate-200 py-2 z-50">
                                    <div class="px-4 py-2 border-b border-slate-100">
                                        <p id="user-name" class="font-semibold text-slate-800">User Name</p>
                                        <p id="user-email" class="text-sm text-slate-500"><EMAIL></p>
                                    </div>
                                    <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                        <i data-lucide="user" class="w-4 h-4"></i>
                                        Profile
                                    </a>
                                    <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                        <i data-lucide="settings" class="w-4 h-4"></i>
                                        Settings
                                    </a>
                                    <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                        <i data-lucide="help-circle" class="w-4 h-4"></i>
                                        Help
                                    </a>
                                    <hr class="my-2 border-slate-200">
                                    <button id="logout-btn" class="w-full flex items-center gap-2 px-4 py-2 text-red-600 hover:bg-red-50 transition-colors">
                                        <i data-lucide="log-out" class="w-4 h-4"></i>
                                        Logout
                                    </button>
                                </div>
                            </div>
                        </div>
                    </header>

                    <!-- Main Content -->
                    <main class="flex-1 overflow-auto p-4 sm:p-6 bg-slate-50">
                    <!-- Orders Header -->
                    <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                        <div class="flex-1">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="p-2 bg-blue-100 rounded-xl">
                                    <i data-lucide="receipt" class="w-6 h-6 text-blue-600"></i>
                                </div>
                                <div>
                                    <h1 class="text-2xl font-bold text-slate-900">Orders Management</h1>
                                    <p class="text-slate-600 text-sm">Real-time order tracking and management</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-4 text-sm text-slate-500">
                                <span class="flex items-center gap-1">
                                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                    Live updates
                                </span>
                                <span>Last updated: Just now</span>
                            </div>
                        </div>
                        <div class="flex flex-wrap gap-3 mt-4 lg:mt-0">
                            <button class="flex items-center gap-2 px-4 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-200 font-medium shadow-lg shadow-blue-500/25">
                                <i data-lucide="plus" class="w-4 h-4"></i>
                                New Order
                            </button>
                            <button class="flex items-center gap-2 px-4 py-2.5 bg-white border border-slate-300 text-slate-700 rounded-xl hover:bg-slate-50 hover:border-slate-400 transition-all duration-200 font-medium">
                                <i data-lucide="download" class="w-4 h-4"></i>
                                Export
                            </button>
                            <button class="flex items-center gap-2 px-4 py-2.5 bg-white border border-slate-300 text-slate-700 rounded-xl hover:bg-slate-50 hover:border-slate-400 transition-all duration-200 font-medium">
                                <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                                Refresh
                            </button>
                        </div>
                    </div>

                    <!-- Order Stats Cards -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <!-- Total Orders Card -->
                        <div class="group bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg hover:border-blue-300 transition-all duration-300 cursor-pointer">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
                                    <i data-lucide="shopping-cart" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-green-600 text-sm font-medium">
                                        <i data-lucide="trending-up" class="w-4 h-4"></i>
                                        +12%
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Total Orders Today</p>
                                <p class="text-3xl font-bold text-slate-900 mb-1">247</p>
                                <p class="text-xs text-slate-600">vs 221 yesterday</p>
                            </div>
                        </div>

                        <!-- Pending Orders Card -->
                        <div class="group bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg hover:border-orange-300 transition-all duration-300 cursor-pointer">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-lg">
                                    <i data-lucide="clock" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-orange-600 text-sm font-medium">
                                        <div class="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                                        Urgent
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Pending Orders</p>
                                <p class="text-3xl font-bold text-orange-600 mb-1">8</p>
                                <p class="text-xs text-slate-600">Awaiting confirmation</p>
                            </div>
                        </div>

                        <!-- In Progress Card -->
                        <div class="group bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg hover:border-purple-300 transition-all duration-300 cursor-pointer">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
                                    <i data-lucide="chef-hat" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-purple-600 text-sm font-medium">
                                        <div class="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                                        Active
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">In Kitchen</p>
                                <p class="text-3xl font-bold text-purple-600 mb-1">15</p>
                                <p class="text-xs text-slate-600">Being prepared</p>
                            </div>
                        </div>

                        <!-- Completed Card -->
                        <div class="group bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg hover:border-green-300 transition-all duration-300 cursor-pointer">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                                    <i data-lucide="check-circle" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-green-600 text-sm font-medium">
                                        <i data-lucide="trending-up" class="w-4 h-4"></i>
                                        +8%
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Completed Today</p>
                                <p class="text-3xl font-bold text-green-600 mb-1">224</p>
                                <p class="text-xs text-slate-600">₹18,450 revenue</p>
                            </div>
                        </div>
                    </div>

                    <!-- Compact Filters and Search -->
                    <div class="bg-white rounded-xl p-4 shadow-sm border border-slate-200 mb-6">
                        <!-- Search Bar -->
                        <div class="mb-4">
                            <div class="relative">
                                <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400"></i>
                                <input type="text" id="order-search" placeholder="Search orders..."
                                       class="w-full pl-10 pr-4 py-2.5 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
                            </div>
                        </div>

                        <!-- Compact Status Filters -->
                        <div class="flex flex-wrap gap-2">
                            <button class="order-filter-btn active px-3 py-1.5 bg-blue-600 text-white rounded-lg text-xs font-medium" data-status="all">
                                All (247)
                            </button>
                            <button class="order-filter-btn px-3 py-1.5 bg-orange-100 text-orange-700 rounded-lg text-xs font-medium hover:bg-orange-200" data-status="pending">
                                Pending (8)
                            </button>
                            <button class="order-filter-btn px-3 py-1.5 bg-purple-100 text-purple-700 rounded-lg text-xs font-medium hover:bg-purple-200" data-status="preparing">
                                Kitchen (15)
                            </button>
                            <button class="order-filter-btn px-3 py-1.5 bg-yellow-100 text-yellow-700 rounded-lg text-xs font-medium hover:bg-yellow-200" data-status="ready">
                                Ready (5)
                            </button>
                            <button class="order-filter-btn px-3 py-1.5 bg-green-100 text-green-700 rounded-lg text-xs font-medium hover:bg-green-200" data-status="completed">
                                Done (224)
                            </button>
                        </div>
                    </div>

                    <!-- Compact Orders Table -->
                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
                        <!-- Simple Table Header -->
                        <div class="px-4 py-3 border-b border-slate-200 bg-slate-50">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-slate-700">Recent Orders</span>
                                <span class="text-xs text-slate-500">10 of 247</span>
                            </div>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-slate-50 border-b border-slate-200">
                                    <tr>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs">Order</th>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs">Customer</th>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs hidden sm:table-cell">Table</th>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs">Items</th>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs">Total</th>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs">Status</th>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs hidden lg:table-cell">Time</th>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="orders-table-body" class="divide-y divide-slate-100">
                                    <!-- Compact Order Rows -->
                                    <tr class="hover:bg-slate-50 transition-colors" data-status="pending">
                                        <td class="py-3 px-4">
                                            <div class="font-medium text-slate-900 text-sm">#ORD-001</div>
                                            <div class="text-xs text-slate-500">2 min ago</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="font-medium text-slate-900 text-sm">John Smith</div>
                                            <div class="text-xs text-slate-500">+****************</div>
                                        </td>
                                        <td class="py-3 px-4 hidden sm:table-cell">
                                            <span class="font-medium text-slate-900 text-sm">Table 5</span>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="text-sm text-slate-900">2x Burger, 1x Fries</div>
                                            <div class="text-xs text-blue-600">+1 more</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="font-bold text-slate-900">₹850</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                                Pending
                                            </span>
                                        </td>
                                        <td class="py-3 px-4 hidden lg:table-cell">
                                            <div class="text-sm text-slate-900">2:34 PM</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="flex gap-1">
                                                <button class="px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700">
                                                    Accept
                                                </button>
                                                <button class="px-2 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700">
                                                    Reject
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <tr class="hover:bg-slate-50 transition-colors" data-status="preparing">
                                        <td class="py-3 px-4">
                                            <div class="font-medium text-slate-900 text-sm">#ORD-002</div>
                                            <div class="text-xs text-slate-500">8 min ago</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="font-medium text-slate-900 text-sm">Sarah Johnson</div>
                                            <div class="text-xs text-slate-500">+****************</div>
                                        </td>
                                        <td class="py-3 px-4 hidden sm:table-cell">
                                            <span class="font-medium text-slate-900 text-sm">Table 3</span>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="text-sm text-slate-900">1x Pizza, 2x Coke</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="font-bold text-slate-900">₹1,200</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                                Kitchen
                                            </span>
                                        </td>
                                        <td class="py-3 px-4 hidden lg:table-cell">
                                            <div class="text-sm text-slate-900">2:26 PM</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <button class="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700">
                                                Mark Ready
                                            </button>
                                        </td>
                                    </tr>

                                    <tr class="hover:bg-slate-50 transition-colors" data-status="ready">
                                        <td class="py-3 px-4">
                                            <div class="font-medium text-slate-900 text-sm">#ORD-003</div>
                                            <div class="text-xs text-slate-500">12 min ago</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="font-medium text-slate-900 text-sm">Mike Davis</div>
                                            <div class="text-xs text-slate-500">+****************</div>
                                        </td>
                                        <td class="py-3 px-4 hidden sm:table-cell">
                                            <span class="font-medium text-slate-900 text-sm">Table 7</span>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="text-sm text-slate-900">1x Pasta, 1x Salad</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="font-bold text-slate-900">₹950</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                Ready
                                            </span>
                                        </td>
                                        <td class="py-3 px-4 hidden lg:table-cell">
                                            <div class="text-sm text-slate-900">2:22 PM</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <button class="px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700">
                                                Serve
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Simple Pagination -->
                        <div class="bg-slate-50 px-4 py-3 border-t border-slate-200">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-slate-600">
                                    Showing 1-10 of 247 orders
                                </div>
                                <div class="flex items-center gap-2">
                                    <button class="px-3 py-1.5 text-sm border border-slate-300 rounded hover:bg-white" disabled>
                                        Previous
                                    </button>
                                    <button class="px-3 py-1.5 text-sm bg-blue-600 text-white rounded">1</button>
                                    <button class="px-3 py-1.5 text-sm border border-slate-300 rounded hover:bg-white">2</button>
                                    <button class="px-3 py-1.5 text-sm border border-slate-300 rounded hover:bg-white">3</button>
                                    <button class="px-3 py-1.5 text-sm border border-slate-300 rounded hover:bg-white">
                                        Next
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    </main>
                </div>

                <!-- Tasks Content -->
                <div id="tasks-content" class="content-section">
                    <!-- Header -->
                    <header class="flex justify-between items-center p-4 sm:p-6 bg-white border-b">
                        <div class="flex items-center">
                            <!-- Mobile Menu Button -->
                            <button id="mobile-menu-button" class="lg:hidden mr-4 text-slate-600 hover:text-slate-900">
                                <i data-lucide="menu" class="w-6 h-6"></i>
                            </button>
                            <h2 id="header-title" class="text-2xl font-semibold text-slate-800">Task Board</h2>
                        </div>
                        <div class="flex items-center space-x-4">
                            <!-- User Role Badge -->
                            <div id="user-role-badge" class="hidden lg:flex items-center gap-2 px-3 py-2 bg-blue-100 text-blue-800 rounded-lg text-sm font-medium">
                                <span id="role-icon">👨‍💼</span>
                                <span id="role-text">Admin</span>
                            </div>

                            <button class="text-slate-600 hover:text-blue-600"><i data-lucide="bell" class="w-6 h-6"></i></button>

                            <!-- User Menu -->
                            <div class="relative">
                                <button id="user-menu-btn" class="flex items-center gap-2 text-slate-600 hover:text-slate-800 transition-colors">
                                    <img id="user-avatar" class="h-10 w-10 rounded-full object-cover" src="https://placehold.co/100x100/E2E8F0/475569?text=AV" alt="User avatar">
                                    <span class="absolute right-0 bottom-0 h-3 w-3 bg-green-500 rounded-full border-2 border-white"></span>
                                    <i data-lucide="chevron-down" class="w-4 h-4"></i>
                                </button>

                                <!-- Dropdown Menu -->
                                <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-slate-200 py-2 z-50">
                                    <div class="px-4 py-2 border-b border-slate-100">
                                        <p id="user-name" class="font-semibold text-slate-800">User Name</p>
                                        <p id="user-email" class="text-sm text-slate-500"><EMAIL></p>
                                    </div>
                                    <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                        <i data-lucide="user" class="w-4 h-4"></i>
                                        Profile
                                    </a>
                                    <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                        <i data-lucide="settings" class="w-4 h-4"></i>
                                        Settings
                                    </a>
                                    <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                        <i data-lucide="help-circle" class="w-4 h-4"></i>
                                        Help
                                    </a>
                                    <hr class="my-2 border-slate-200">
                                    <button id="logout-btn" class="w-full flex items-center gap-2 px-4 py-2 text-red-600 hover:bg-red-50 transition-colors">
                                        <i data-lucide="log-out" class="w-4 h-4"></i>
                                        Logout
                                    </button>
                                </div>
                            </div>
                        </div>
                    </header>

                    <!-- Main Content -->
                    <main class="flex-1 overflow-auto p-4 sm:p-6 bg-slate-50">
                        <!-- Clean Header - Matching Design -->
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 mb-8">
                            <div class="flex items-center justify-between">
                                <!-- Left: Icon + Title + Description -->
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center">
                                        <i data-lucide="kanban-square" class="w-5 h-5 text-white"></i>
                                    </div>
                                    <div>
                                        <h1 class="text-xl font-bold text-slate-900 mb-1">Task Management</h1>
                                        <p class="text-sm text-slate-500">Organize, assign, and track team tasks efficiently</p>
                                    </div>
                                </div>

                                <!-- Right: Action Buttons -->
                                <div class="flex items-center gap-3">
                                    <button id="add-task-btn" class="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors font-medium text-sm">
                                        <i data-lucide="plus" class="w-4 h-4"></i>
                                        Add Task
                                    </button>
                                    <button class="flex items-center gap-2 px-4 py-2 bg-white border border-slate-300 text-slate-600 rounded-lg hover:bg-slate-50 transition-colors font-medium text-sm">
                                        <i data-lucide="filter" class="w-4 h-4"></i>
                                        Filter
                                    </button>
                                    <button class="flex items-center gap-2 px-4 py-2 bg-white border border-slate-300 text-slate-600 rounded-lg hover:bg-slate-50 transition-colors font-medium text-sm">
                                        <i data-lucide="users" class="w-4 h-4"></i>
                                        Team
                                    </button>
                                </div>
                            </div>

                            <!-- Bottom: Status Indicators -->
                            <div class="flex items-center gap-6 mt-4 pt-4 border-t border-slate-100">
                                <div class="flex items-center gap-2 text-sm">
                                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                    <span class="text-slate-600">Real-time updates</span>
                                </div>
                                <div class="flex items-center gap-2 text-sm">
                                    <span class="font-medium text-slate-900">5</span>
                                    <span class="text-slate-600">active tasks</span>
                                </div>
                                <div class="flex items-center gap-2 text-sm">
                                    <span class="font-medium text-slate-900">3</span>
                                    <span class="text-slate-600">team members</span>
                                </div>
                            </div>
                        </div>

                        <!-- Task Statistics -->
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                            <div class="bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-shadow">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
                                        <i data-lucide="list-todo" class="w-6 h-6 text-white"></i>
                                    </div>
                                    <span class="text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded-full">+2 today</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-slate-500 mb-1">Total Tasks</p>
                                    <p class="text-3xl font-bold text-slate-900">12</p>
                                </div>
                            </div>

                            <div class="bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-shadow">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-lg">
                                        <i data-lucide="clock" class="w-6 h-6 text-white"></i>
                                    </div>
                                    <span class="text-xs font-medium text-orange-600 bg-orange-100 px-2 py-1 rounded-full">Pending</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-slate-500 mb-1">To Do</p>
                                    <p class="text-3xl font-bold text-orange-600">5</p>
                                </div>
                            </div>

                            <div class="bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-shadow">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
                                        <i data-lucide="play-circle" class="w-6 h-6 text-white"></i>
                                    </div>
                                    <span class="text-xs font-medium text-purple-600 bg-purple-100 px-2 py-1 rounded-full">Active</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-slate-500 mb-1">In Progress</p>
                                    <p class="text-3xl font-bold text-purple-600">3</p>
                                </div>
                            </div>

                            <div class="bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-shadow">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                                        <i data-lucide="check-circle" class="w-6 h-6 text-white"></i>
                                    </div>
                                    <span class="text-xs font-medium text-green-600 bg-green-100 px-2 py-1 rounded-full">+4 today</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-slate-500 mb-1">Completed</p>
                                    <p class="text-3xl font-bold text-green-600">4</p>
                                </div>
                            </div>
                        </div>

                        <!-- Task List View -->
                        <div class="space-y-3">
                            <!-- All Tasks Container -->
                            <div id="all-tasks" class="tasks-container space-y-3">
                                <!-- Task Row 1 - To Do -->
                                <div id="task-1" class="task-card bg-white p-4 cursor-pointer hover:bg-slate-50 transition-all duration-200 group rounded-lg border border-slate-200 shadow-sm" draggable="true" data-status="todo">
                                    <div class="flex items-center gap-4">
                                        <!-- Left: Status & Priority -->
                                        <div class="flex items-center gap-3 flex-shrink-0">
                                            <div class="w-1 h-6 bg-orange-500 rounded-full"></div>
                                            <span class="text-xs font-semibold text-orange-600 bg-orange-50 px-2.5 py-1 rounded-md border border-orange-200">TO DO</span>
                                            <span class="text-xs font-semibold text-red-600 bg-red-50 px-2.5 py-1 rounded-md border border-red-200">HIGH</span>
                                        </div>

                                        <!-- Center: Task Content -->
                                        <div class="flex-1 min-w-0">
                                            <h5 class="font-semibold text-slate-900 mb-1 truncate">Clean the main dining area</h5>
                                            <p class="text-sm text-slate-600 line-clamp-1">Wipe down all tables, chairs, and sanitize surfaces before opening.</p>
                                        </div>

                                        <!-- Right: Metadata -->
                                        <div class="flex items-center gap-4 flex-shrink-0">
                                            <!-- Assignees -->
                                            <div class="flex -space-x-1">
                                                <img class="h-6 w-6 rounded-full object-cover border-2 border-white shadow-sm" src="https://placehold.co/100x100/E0F2FE/0891B2?text=JD" alt="John Doe">
                                                <img class="h-6 w-6 rounded-full object-cover border-2 border-white shadow-sm" src="https://placehold.co/100x100/FEE2E2/B91C1C?text=SM" alt="Sarah Miller">
                                            </div>

                                            <!-- Time & Category -->
                                            <div class="flex items-center gap-3">
                                                <div class="flex items-center gap-1 text-xs text-slate-500 bg-slate-50 px-2 py-1 rounded border">
                                                    <i data-lucide="clock" class="w-3 h-3"></i>
                                                    <span>30m</span>
                                                </div>
                                                <span class="px-2.5 py-1 text-xs font-medium text-blue-700 bg-blue-50 rounded-md border border-blue-200">Cleaning</span>
                                            </div>

                                            <!-- Actions -->
                                            <button class="opacity-0 group-hover:opacity-100 p-1.5 hover:bg-slate-100 rounded-lg transition-all">
                                                <i data-lucide="more-horizontal" class="w-4 h-4 text-slate-400"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Task Row 2 - To Do -->
                                <div id="task-2" class="task-card bg-white p-4 cursor-pointer hover:bg-slate-50 transition-all duration-200 group rounded-lg border border-slate-200 shadow-sm" draggable="true" data-status="todo">
                                    <div class="flex items-center gap-4">
                                        <!-- Left: Status & Priority -->
                                        <div class="flex items-center gap-3 flex-shrink-0">
                                            <div class="w-1 h-6 bg-orange-500 rounded-full"></div>
                                            <span class="text-xs font-semibold text-orange-600 bg-orange-50 px-2.5 py-1 rounded-md border border-orange-200">TO DO</span>
                                            <span class="text-xs font-semibold text-yellow-600 bg-yellow-50 px-2.5 py-1 rounded-md border border-yellow-200">MED</span>
                                        </div>

                                        <!-- Center: Task Content -->
                                        <div class="flex-1 min-w-0">
                                            <h5 class="font-semibold text-slate-900 mb-1 truncate">Restock bar supplies</h5>
                                            <p class="text-sm text-slate-600 line-clamp-1">Check inventory for lemons, limes, and other bar essentials.</p>
                                        </div>

                                        <!-- Right: Metadata -->
                                        <div class="flex items-center gap-4 flex-shrink-0">
                                            <!-- Assignees -->
                                            <div class="flex -space-x-1">
                                                <img class="h-6 w-6 rounded-full object-cover border-2 border-white shadow-sm" src="https://placehold.co/100x100/D1FAE5/047857?text=KW" alt="Kevin Wang">
                                            </div>

                                            <!-- Time & Category -->
                                            <div class="flex items-center gap-3">
                                                <div class="flex items-center gap-1 text-xs text-slate-500 bg-slate-50 px-2 py-1 rounded border">
                                                    <i data-lucide="clock" class="w-3 h-3"></i>
                                                    <span>45m</span>
                                                </div>
                                                <span class="px-2.5 py-1 text-xs font-medium text-indigo-700 bg-indigo-50 rounded-md border border-indigo-200">Inventory</span>
                                            </div>

                                            <!-- Actions -->
                                            <button class="opacity-0 group-hover:opacity-100 p-1.5 hover:bg-slate-100 rounded-lg transition-all">
                                                <i data-lucide="more-horizontal" class="w-4 h-4 text-slate-400"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Task Row 3 - In Progress -->
                                <div id="task-3" class="task-card bg-white p-4 cursor-pointer hover:bg-slate-50 transition-all duration-200 group rounded-lg border border-slate-200 shadow-sm" draggable="true" data-status="inprogress">
                                    <div class="flex items-center gap-4">
                                        <!-- Left: Status & Priority -->
                                        <div class="flex items-center gap-3 flex-shrink-0">
                                            <div class="w-1 h-6 bg-purple-500 rounded-full animate-pulse"></div>
                                            <span class="text-xs font-semibold text-purple-600 bg-purple-50 px-2.5 py-1 rounded-md border border-purple-200">IN PROGRESS</span>
                                            <span class="text-xs font-semibold text-yellow-600 bg-yellow-50 px-2.5 py-1 rounded-md border border-yellow-200">MED</span>
                                        </div>

                                        <!-- Center: Task Content with Progress -->
                                        <div class="flex-1 min-w-0">
                                            <h5 class="font-semibold text-slate-900 mb-1 truncate">Prepare daily specials</h5>
                                            <div class="flex items-center gap-3">
                                                <p class="text-sm text-slate-600 line-clamp-1 flex-1">Prep ingredients for the Chef's special pasta dish.</p>
                                                <!-- Inline Progress Bar -->
                                                <div class="flex items-center gap-2">
                                                    <div class="w-20 bg-slate-200 rounded-full h-1.5">
                                                        <div class="bg-purple-500 h-1.5 rounded-full transition-all duration-300" style="width: 65%"></div>
                                                    </div>
                                                    <span class="text-xs text-purple-600 font-semibold">65%</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Right: Metadata -->
                                        <div class="flex items-center gap-4 flex-shrink-0">
                                            <!-- Assignees -->
                                            <div class="flex -space-x-1">
                                                <img class="h-6 w-6 rounded-full object-cover border-2 border-white shadow-sm" src="https://placehold.co/100x100/EEF2FF/4338CA?text=EB" alt="Emily Brown">
                                            </div>

                                            <!-- Time & Category -->
                                            <div class="flex items-center gap-3">
                                                <div class="flex items-center gap-1 text-xs text-slate-500 bg-slate-50 px-2 py-1 rounded border">
                                                    <i data-lucide="clock" class="w-3 h-3"></i>
                                                    <span>20m left</span>
                                                </div>
                                                <span class="px-2.5 py-1 text-xs font-medium text-amber-700 bg-amber-50 rounded-md border border-amber-200">Kitchen</span>
                                            </div>

                                            <!-- Actions -->
                                            <button class="opacity-0 group-hover:opacity-100 p-1.5 hover:bg-slate-100 rounded-lg transition-all">
                                                <i data-lucide="more-horizontal" class="w-4 h-4 text-slate-400"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Task Row 4 - Done -->
                                <div id="task-4" class="task-card bg-white p-4 cursor-pointer hover:bg-slate-50 transition-all duration-200 group rounded-lg border border-slate-200 shadow-sm" draggable="true" data-status="done">
                                    <div class="flex items-center gap-4">
                                        <!-- Left: Status & Priority -->
                                        <div class="flex items-center gap-3 flex-shrink-0">
                                            <div class="w-1 h-6 bg-green-500 rounded-full"></div>
                                            <span class="text-xs font-semibold text-green-600 bg-green-50 px-2.5 py-1 rounded-md border border-green-200">DONE</span>
                                            <span class="text-xs font-semibold text-green-600 bg-green-50 px-2.5 py-1 rounded-md border border-green-200">LOW</span>
                                        </div>

                                        <!-- Center: Task Content with Completion -->
                                        <div class="flex-1 min-w-0">
                                            <h5 class="font-semibold text-slate-900 mb-1 truncate">Set up outdoor seating</h5>
                                            <div class="flex items-center gap-3">
                                                <p class="text-sm text-slate-600 line-clamp-1 flex-1">Arrange tables and umbrellas on the patio.</p>
                                                <!-- Completion Status -->
                                                <div class="flex items-center gap-2 text-xs">
                                                    <i data-lucide="check-circle" class="w-3 h-3 text-green-600"></i>
                                                    <span class="text-green-700 font-medium">2h ago</span>
                                                    <span class="text-slate-300">•</span>
                                                    <span class="text-green-600 font-medium">On time</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Right: Metadata -->
                                        <div class="flex items-center gap-4 flex-shrink-0">
                                            <!-- Assignees -->
                                            <div class="flex items-center gap-2">
                                                <div class="flex -space-x-1">
                                                    <img class="h-6 w-6 rounded-full object-cover border-2 border-white shadow-sm" src="https://placehold.co/100x100/E0F2FE/0891B2?text=JD" alt="John Doe">
                                                </div>
                                                <span class="text-xs text-slate-500 font-medium">John</span>
                                            </div>

                                            <!-- Category -->
                                            <div class="flex items-center gap-3">
                                                <span class="px-2.5 py-1 text-xs font-medium text-green-700 bg-green-50 rounded-md border border-green-200">Setup</span>
                                            </div>

                                            <!-- Actions -->
                                            <button class="opacity-0 group-hover:opacity-100 p-1.5 hover:bg-slate-100 rounded-lg transition-all">
                                                <i data-lucide="more-horizontal" class="w-4 h-4 text-slate-400"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Add Task Button -->
                                <button class="w-full p-4 border-2 border-dashed border-slate-300 rounded-lg text-slate-500 hover:border-purple-300 hover:text-purple-600 hover:bg-purple-50 transition-all duration-200 flex items-center justify-center gap-2">
                                    <i data-lucide="plus" class="w-4 h-4"></i>
                                    Add new task
                                </button>
                            </div>
                        </div>
                    </main>
                </div>

                <!-- Tables Content -->
                <div id="tables-content" class="content-section">
                    <!-- Header -->
                    <header class="flex justify-between items-center p-4 sm:p-6 bg-white border-b">
                        <div class="flex items-center">
                            <!-- Mobile Menu Button -->
                            <button id="mobile-menu-button" class="lg:hidden mr-4 text-slate-600 hover:text-slate-900">
                                <i data-lucide="menu" class="w-6 h-6"></i>
                            </button>
                            <h2 id="header-title" class="text-2xl font-semibold text-slate-800">Table Management</h2>
                        </div>
                        <div class="flex items-center space-x-4">
                            <!-- User Role Badge -->
                            <div id="user-role-badge" class="hidden lg:flex items-center gap-2 px-3 py-2 bg-blue-100 text-blue-800 rounded-lg text-sm font-medium">
                                <span id="role-icon">👨‍💼</span>
                                <span id="role-text">Admin</span>
                            </div>

                            <button class="text-slate-600 hover:text-blue-600"><i data-lucide="bell" class="w-6 h-6"></i></button>

                            <!-- User Menu -->
                            <div class="relative">
                                <button id="user-menu-btn" class="flex items-center gap-2 text-slate-600 hover:text-slate-800 transition-colors">
                                    <img id="user-avatar" class="h-10 w-10 rounded-full object-cover" src="https://placehold.co/100x100/E2E8F0/475569?text=AV" alt="User avatar">
                                    <span class="absolute right-0 bottom-0 h-3 w-3 bg-green-500 rounded-full border-2 border-white"></span>
                                    <i data-lucide="chevron-down" class="w-4 h-4"></i>
                                </button>

                                <!-- Dropdown Menu -->
                                <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-slate-200 py-2 z-50">
                                    <div class="px-4 py-2 border-b border-slate-100">
                                        <p id="user-name" class="font-semibold text-slate-800">User Name</p>
                                        <p id="user-email" class="text-sm text-slate-500"><EMAIL></p>
                                    </div>
                                    <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                        <i data-lucide="user" class="w-4 h-4"></i>
                                        Profile
                                    </a>
                                    <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                        <i data-lucide="settings" class="w-4 h-4"></i>
                                        Settings
                                    </a>
                                    <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                        <i data-lucide="help-circle" class="w-4 h-4"></i>
                                        Help
                                    </a>
                                    <hr class="my-2 border-slate-200">
                                    <button id="logout-btn" class="w-full flex items-center gap-2 px-4 py-2 text-red-600 hover:bg-red-50 transition-colors">
                                        <i data-lucide="log-out" class="w-4 h-4"></i>
                                        Logout
                                    </button>
                                </div>
                            </div>
                        </div>
                    </header>

                    <!-- Main Content -->
                    <main class="flex-1 overflow-auto p-4 sm:p-6 bg-slate-50">
                        <!-- Header Section -->
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 mb-8">
                            <div class="flex items-center justify-between">
                                <!-- Left: Icon + Title + Description -->
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center">
                                        <i data-lucide="layout" class="w-5 h-5 text-white"></i>
                                    </div>
                                    <div>
                                        <h1 class="text-xl font-bold text-slate-900 mb-1">Table Management</h1>
                                        <p class="text-sm text-slate-500">Monitor and manage restaurant table status and reservations</p>
                                    </div>
                                </div>

                                <!-- Right: Action Buttons -->
                                <div class="flex items-center gap-3">
                                    <button id="add-table-btn" class="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium text-sm">
                                        <i data-lucide="plus" class="w-4 h-4"></i>
                                        Add Table
                                    </button>
                                    <button class="flex items-center gap-2 px-4 py-2 bg-white border border-slate-300 text-slate-600 rounded-lg hover:bg-slate-50 transition-colors font-medium text-sm">
                                        <i data-lucide="settings" class="w-4 h-4"></i>
                                        Layout
                                    </button>
                                    <button class="flex items-center gap-2 px-4 py-2 bg-white border border-slate-300 text-slate-600 rounded-lg hover:bg-slate-50 transition-colors font-medium text-sm">
                                        <i data-lucide="calendar" class="w-4 h-4"></i>
                                        Reservations
                                    </button>
                                </div>
                            </div>

                            <!-- Bottom: Status Indicators -->
                            <div class="flex items-center gap-6 mt-4 pt-4 border-t border-slate-100">
                                <div class="flex items-center gap-2 text-sm">
                                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                    <span class="text-slate-600">Real-time status</span>
                                </div>
                                <div class="flex items-center gap-2 text-sm">
                                    <span class="font-medium text-slate-900">12</span>
                                    <span class="text-slate-600">occupied tables</span>
                                </div>
                                <div class="flex items-center gap-2 text-sm">
                                    <span class="font-medium text-slate-900">4</span>
                                    <span class="text-slate-600">available tables</span>
                                </div>
                                <div class="flex items-center gap-2 text-sm">
                                    <span class="font-medium text-slate-900">8</span>
                                    <span class="text-slate-600">reservations today</span>
                                </div>
                            </div>
                        </div>

                        <!-- Table Statistics -->
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                            <div class="bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-shadow">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                                        <i data-lucide="check-circle" class="w-6 h-6 text-white"></i>
                                    </div>
                                    <span class="text-xs font-medium text-green-600 bg-green-100 px-2 py-1 rounded-full">Available</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-slate-500 mb-1">Available Tables</p>
                                    <p class="text-3xl font-bold text-green-600">4</p>
                                </div>
                            </div>

                            <div class="bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-shadow">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="p-3 bg-gradient-to-br from-red-500 to-red-600 rounded-xl shadow-lg">
                                        <i data-lucide="users" class="w-6 h-6 text-white"></i>
                                    </div>
                                    <span class="text-xs font-medium text-red-600 bg-red-100 px-2 py-1 rounded-full">Occupied</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-slate-500 mb-1">Occupied Tables</p>
                                    <p class="text-3xl font-bold text-red-600">12</p>
                                </div>
                            </div>

                            <div class="bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-shadow">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="p-3 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl shadow-lg">
                                        <i data-lucide="calendar-clock" class="w-6 h-6 text-white"></i>
                                    </div>
                                    <span class="text-xs font-medium text-yellow-600 bg-yellow-100 px-2 py-1 rounded-full">Reserved</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-slate-500 mb-1">Reserved Tables</p>
                                    <p class="text-3xl font-bold text-yellow-600">3</p>
                                </div>
                            </div>

                            <div class="bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-shadow">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
                                        <i data-lucide="percent" class="w-6 h-6 text-white"></i>
                                    </div>
                                    <span class="text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded-full">75%</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-slate-500 mb-1">Occupancy Rate</p>
                                    <p class="text-3xl font-bold text-blue-600">75%</p>
                                </div>
                            </div>
                        </div>

                        <!-- Filters and Controls -->
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 mb-8">
                            <div class="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
                                <!-- Left: Filters -->
                                <div class="flex flex-wrap items-center gap-3">
                                    <span class="text-sm font-medium text-slate-700">Filter by status:</span>
                                    <div class="flex gap-2">
                                        <button class="table-filter-btn active px-3 py-1.5 text-xs font-medium bg-slate-600 text-white rounded-lg hover:bg-slate-700 transition-colors" data-status="all">
                                            All Tables
                                        </button>
                                        <button class="table-filter-btn px-3 py-1.5 text-xs font-medium bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors" data-status="available">
                                            Available
                                        </button>
                                        <button class="table-filter-btn px-3 py-1.5 text-xs font-medium bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors" data-status="occupied">
                                            Occupied
                                        </button>
                                        <button class="table-filter-btn px-3 py-1.5 text-xs font-medium bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors" data-status="reserved">
                                            Reserved
                                        </button>
                                        <button class="table-filter-btn px-3 py-1.5 text-xs font-medium bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors" data-status="cleaning">
                                            Cleaning
                                        </button>
                                    </div>
                                </div>

                                <!-- Right: Search and View Toggle -->
                                <div class="flex items-center gap-3">
                                    <div class="relative">
                                        <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400"></i>
                                        <input type="text" id="table-search" placeholder="Search tables..." class="pl-10 pr-4 py-2 border border-slate-300 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 w-64">
                                    </div>
                                    <div class="flex bg-slate-100 rounded-lg p-1">
                                        <button id="grid-view-btn" class="view-toggle-btn active px-3 py-1.5 text-xs font-medium bg-white text-slate-700 rounded-md shadow-sm">
                                            <i data-lucide="grid-3x3" class="w-4 h-4"></i>
                                        </button>
                                        <button id="list-view-btn" class="view-toggle-btn px-3 py-1.5 text-xs font-medium text-slate-500 rounded-md">
                                            <i data-lucide="list" class="w-4 h-4"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Table Grid -->
                        <div id="table-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                            <!-- Table Card 1 - Available -->
                            <div class="table-card bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-200 cursor-pointer" data-status="available" data-table="1">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                            <i data-lucide="check-circle" class="w-5 h-5 text-green-600"></i>
                                        </div>
                                        <div>
                                            <h3 class="font-semibold text-slate-900">Table 1</h3>
                                            <p class="text-sm text-slate-500">4 seats</p>
                                        </div>
                                    </div>
                                    <span class="px-2.5 py-1 text-xs font-medium text-green-700 bg-green-100 rounded-full">Available</span>
                                </div>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-slate-600">Location:</span>
                                        <span class="font-medium text-slate-900">Main Floor</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-slate-600">Last cleaned:</span>
                                        <span class="font-medium text-slate-900">30 min ago</span>
                                    </div>
                                </div>
                                <div class="mt-4 pt-4 border-t border-slate-100">
                                    <button class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium">
                                        Reserve Table
                                    </button>
                                </div>
                            </div>

                            <!-- Table Card 2 - Occupied -->
                            <div class="table-card bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-200 cursor-pointer" data-status="occupied" data-table="2">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                                            <i data-lucide="users" class="w-5 h-5 text-red-600"></i>
                                        </div>
                                        <div>
                                            <h3 class="font-semibold text-slate-900">Table 2</h3>
                                            <p class="text-sm text-slate-500">2 seats</p>
                                        </div>
                                    </div>
                                    <span class="px-2.5 py-1 text-xs font-medium text-red-700 bg-red-100 rounded-full">Occupied</span>
                                </div>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-slate-600">Customer:</span>
                                        <span class="font-medium text-slate-900">John Smith</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-slate-600">Seated at:</span>
                                        <span class="font-medium text-slate-900">7:30 PM</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-slate-600">Duration:</span>
                                        <span class="font-medium text-red-600">45 min</span>
                                    </div>
                                </div>
                                <div class="mt-4 pt-4 border-t border-slate-100">
                                    <button class="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-medium">
                                        Check Bill
                                    </button>
                                </div>
                            </div>

                            <!-- Table Card 3 - Reserved -->
                            <div class="table-card bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-200 cursor-pointer" data-status="reserved" data-table="3">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                                            <i data-lucide="calendar-clock" class="w-5 h-5 text-yellow-600"></i>
                                        </div>
                                        <div>
                                            <h3 class="font-semibold text-slate-900">Table 3</h3>
                                            <p class="text-sm text-slate-500">6 seats</p>
                                        </div>
                                    </div>
                                    <span class="px-2.5 py-1 text-xs font-medium text-yellow-700 bg-yellow-100 rounded-full">Reserved</span>
                                </div>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-slate-600">Reserved for:</span>
                                        <span class="font-medium text-slate-900">Sarah Johnson</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-slate-600">Time:</span>
                                        <span class="font-medium text-slate-900">8:00 PM</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-slate-600">Party size:</span>
                                        <span class="font-medium text-slate-900">4 people</span>
                                    </div>
                                </div>
                                <div class="mt-4 pt-4 border-t border-slate-100">
                                    <button class="w-full px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors text-sm font-medium">
                                        View Reservation
                                    </button>
                                </div>
                            </div>

                            <!-- Table Card 4 - Cleaning -->
                            <div class="table-card bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-200 cursor-pointer" data-status="cleaning" data-table="4">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <i data-lucide="spray-can" class="w-5 h-5 text-blue-600"></i>
                                        </div>
                                        <div>
                                            <h3 class="font-semibold text-slate-900">Table 4</h3>
                                            <p class="text-sm text-slate-500">8 seats</p>
                                        </div>
                                    </div>
                                    <span class="px-2.5 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded-full">Cleaning</span>
                                </div>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-slate-600">Staff:</span>
                                        <span class="font-medium text-slate-900">Maria Garcia</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-slate-600">Started:</span>
                                        <span class="font-medium text-slate-900">5 min ago</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-slate-600">Est. completion:</span>
                                        <span class="font-medium text-blue-600">10 min</span>
                                    </div>
                                </div>
                                <div class="mt-4 pt-4 border-t border-slate-100">
                                    <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                                        Mark Clean
                                    </button>
                                </div>
                            </div>

                            <!-- Table Card 5 - Available -->
                            <div class="table-card bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-200 cursor-pointer" data-status="available" data-table="5">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                            <i data-lucide="check-circle" class="w-5 h-5 text-green-600"></i>
                                        </div>
                                        <div>
                                            <h3 class="font-semibold text-slate-900">Table 5</h3>
                                            <p class="text-sm text-slate-500">4 seats</p>
                                        </div>
                                    </div>
                                    <span class="px-2.5 py-1 text-xs font-medium text-green-700 bg-green-100 rounded-full">Available</span>
                                </div>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-slate-600">Location:</span>
                                        <span class="font-medium text-slate-900">Patio</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-slate-600">Last cleaned:</span>
                                        <span class="font-medium text-slate-900">15 min ago</span>
                                    </div>
                                </div>
                                <div class="mt-4 pt-4 border-t border-slate-100">
                                    <button class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium">
                                        Reserve Table
                                    </button>
                                </div>
                            </div>

                            <!-- Table Card 6 - Occupied -->
                            <div class="table-card bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-200 cursor-pointer" data-status="occupied" data-table="6">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                                            <i data-lucide="users" class="w-5 h-5 text-red-600"></i>
                                        </div>
                                        <div>
                                            <h3 class="font-semibold text-slate-900">Table 6</h3>
                                            <p class="text-sm text-slate-500">2 seats</p>
                                        </div>
                                    </div>
                                    <span class="px-2.5 py-1 text-xs font-medium text-red-700 bg-red-100 rounded-full">Occupied</span>
                                </div>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-slate-600">Customer:</span>
                                        <span class="font-medium text-slate-900">Emily Davis</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-slate-600">Seated at:</span>
                                        <span class="font-medium text-slate-900">7:15 PM</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-slate-600">Duration:</span>
                                        <span class="font-medium text-red-600">1h 15min</span>
                                    </div>
                                </div>
                                <div class="mt-4 pt-4 border-t border-slate-100">
                                    <button class="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-medium">
                                        Check Bill
                                    </button>
                                </div>
                            </div>

                            <!-- Table Card 7 - Available -->
                            <div class="table-card bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-200 cursor-pointer" data-status="available" data-table="7">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                            <i data-lucide="check-circle" class="w-5 h-5 text-green-600"></i>
                                        </div>
                                        <div>
                                            <h3 class="font-semibold text-slate-900">Table 7</h3>
                                            <p class="text-sm text-slate-500">6 seats</p>
                                        </div>
                                    </div>
                                    <span class="px-2.5 py-1 text-xs font-medium text-green-700 bg-green-100 rounded-full">Available</span>
                                </div>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-slate-600">Location:</span>
                                        <span class="font-medium text-slate-900">Private Room</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-slate-600">Last cleaned:</span>
                                        <span class="font-medium text-slate-900">1h ago</span>
                                    </div>
                                </div>
                                <div class="mt-4 pt-4 border-t border-slate-100">
                                    <button class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium">
                                        Reserve Table
                                    </button>
                                </div>
                            </div>

                            <!-- Table Card 8 - Reserved -->
                            <div class="table-card bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-200 cursor-pointer" data-status="reserved" data-table="8">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                                            <i data-lucide="calendar-clock" class="w-5 h-5 text-yellow-600"></i>
                                        </div>
                                        <div>
                                            <h3 class="font-semibold text-slate-900">Table 8</h3>
                                            <p class="text-sm text-slate-500">4 seats</p>
                                        </div>
                                    </div>
                                    <span class="px-2.5 py-1 text-xs font-medium text-yellow-700 bg-yellow-100 rounded-full">Reserved</span>
                                </div>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-slate-600">Reserved for:</span>
                                        <span class="font-medium text-slate-900">Mike Wilson</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-slate-600">Time:</span>
                                        <span class="font-medium text-slate-900">9:00 PM</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-slate-600">Party size:</span>
                                        <span class="font-medium text-slate-900">2 people</span>
                                    </div>
                                </div>
                                <div class="mt-4 pt-4 border-t border-slate-100">
                                    <button class="w-full px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors text-sm font-medium">
                                        View Reservation
                                    </button>
                                </div>
                            </div>
                        </div>
                    </main>
                </div>

                <!-- Placeholder sections -->
                <div id="customers-content" class="content-section">
                    <!-- Header -->
                    <header class="flex justify-between items-center p-4 sm:p-6 bg-white border-b">
                        <div class="flex items-center">
                            <!-- Mobile Menu Button -->
                            <button id="mobile-menu-button" class="lg:hidden mr-4 text-slate-600 hover:text-slate-900">
                                <i data-lucide="menu" class="w-6 h-6"></i>
                            </button>
                            <h2 id="header-title" class="text-2xl font-semibold text-slate-800">Customers</h2>
                        </div>
                        <div class="flex items-center space-x-4">
                            <!-- User Role Badge -->
                            <div id="user-role-badge" class="hidden lg:flex items-center gap-2 px-3 py-2 bg-blue-100 text-blue-800 rounded-lg text-sm font-medium">
                                <span id="role-icon">👨‍💼</span>
                                <span id="role-text">Admin</span>
                            </div>

                            <button class="text-slate-600 hover:text-blue-600"><i data-lucide="bell" class="w-6 h-6"></i></button>

                            <!-- User Menu -->
                            <div class="relative">
                                <button id="user-menu-btn" class="flex items-center gap-2 text-slate-600 hover:text-slate-800 transition-colors">
                                    <img id="user-avatar" class="h-10 w-10 rounded-full object-cover" src="https://placehold.co/100x100/E2E8F0/475569?text=AV" alt="User avatar">
                                    <span class="absolute right-0 bottom-0 h-3 w-3 bg-green-500 rounded-full border-2 border-white"></span>
                                    <i data-lucide="chevron-down" class="w-4 h-4"></i>
                                </button>

                                <!-- Dropdown Menu -->
                                <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-slate-200 py-2 z-50">
                                    <div class="px-4 py-2 border-b border-slate-100">
                                        <p id="user-name" class="font-semibold text-slate-800">User Name</p>
                                        <p id="user-email" class="text-sm text-slate-500"><EMAIL></p>
                                    </div>
                                    <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                        <i data-lucide="user" class="w-4 h-4"></i>
                                        Profile
                                    </a>
                                    <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                        <i data-lucide="settings" class="w-4 h-4"></i>
                                        Settings
                                    </a>
                                    <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                        <i data-lucide="help-circle" class="w-4 h-4"></i>
                                        Help
                                    </a>
                                    <hr class="my-2 border-slate-200">
                                    <button id="logout-btn" class="w-full flex items-center gap-2 px-4 py-2 text-red-600 hover:bg-red-50 transition-colors">
                                        <i data-lucide="log-out" class="w-4 h-4"></i>
                                        Logout
                                    </button>
                                </div>
                            </div>
                        </div>
                    </header>
                    <!-- Main Content -->
                    <main class="flex-1 overflow-auto p-4 sm:p-6 bg-slate-50">
                        <!-- Header Section -->
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 mb-8">
                            <div class="flex items-center justify-between">
                                <!-- Left: Icon + Title + Description -->
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                                        <i data-lucide="users" class="w-5 h-5 text-white"></i>
                                    </div>
                                    <div>
                                        <h1 class="text-xl font-bold text-slate-900 mb-1">Customer Management</h1>
                                        <p class="text-sm text-slate-500">Manage customer profiles, preferences, and dining history</p>
                                    </div>
                                </div>

                                <!-- Right: Action Buttons -->
                                <div class="flex items-center gap-3">
                                    <button id="add-customer-btn" class="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium text-sm">
                                        <i data-lucide="user-plus" class="w-4 h-4"></i>
                                        Add Customer
                                    </button>
                                    <button class="flex items-center gap-2 px-4 py-2 bg-white border border-slate-300 text-slate-600 rounded-lg hover:bg-slate-50 transition-colors font-medium text-sm">
                                        <i data-lucide="download" class="w-4 h-4"></i>
                                        Export
                                    </button>
                                    <button class="flex items-center gap-2 px-4 py-2 bg-white border border-slate-300 text-slate-600 rounded-lg hover:bg-slate-50 transition-colors font-medium text-sm">
                                        <i data-lucide="mail" class="w-4 h-4"></i>
                                        Newsletter
                                    </button>
                                </div>
                            </div>

                            <!-- Bottom: Status Indicators -->
                            <div class="flex items-center gap-6 mt-4 pt-4 border-t border-slate-100">
                                <div class="flex items-center gap-2 text-sm">
                                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                    <span class="text-slate-600">Real-time sync</span>
                                </div>
                                <div class="flex items-center gap-2 text-sm">
                                    <span class="font-medium text-slate-900">1,247</span>
                                    <span class="text-slate-600">total customers</span>
                                </div>
                                <div class="flex items-center gap-2 text-sm">
                                    <span class="font-medium text-slate-900">89</span>
                                    <span class="text-slate-600">VIP members</span>
                                </div>
                                <div class="flex items-center gap-2 text-sm">
                                    <span class="font-medium text-slate-900">156</span>
                                    <span class="text-slate-600">visited this month</span>
                                </div>
                            </div>
                        </div>

                        <!-- Customer Statistics -->
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                            <div class="bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-shadow">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
                                        <i data-lucide="users" class="w-6 h-6 text-white"></i>
                                    </div>
                                    <span class="text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded-full">+12 today</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-slate-500 mb-1">Total Customers</p>
                                    <p class="text-3xl font-bold text-blue-600">1,247</p>
                                </div>
                            </div>

                            <div class="bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-shadow">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
                                        <i data-lucide="crown" class="w-6 h-6 text-white"></i>
                                    </div>
                                    <span class="text-xs font-medium text-purple-600 bg-purple-100 px-2 py-1 rounded-full">VIP</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-slate-500 mb-1">VIP Members</p>
                                    <p class="text-3xl font-bold text-purple-600">89</p>
                                </div>
                            </div>

                            <div class="bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-shadow">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                                        <i data-lucide="calendar-check" class="w-6 h-6 text-white"></i>
                                    </div>
                                    <span class="text-xs font-medium text-green-600 bg-green-100 px-2 py-1 rounded-full">This month</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-slate-500 mb-1">Monthly Visits</p>
                                    <p class="text-3xl font-bold text-green-600">156</p>
                                </div>
                            </div>

                            <div class="bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-shadow">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-lg">
                                        <i data-lucide="dollar-sign" class="w-6 h-6 text-white"></i>
                                    </div>
                                    <span class="text-xs font-medium text-orange-600 bg-orange-100 px-2 py-1 rounded-full">Average</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-slate-500 mb-1">Avg. Spend</p>
                                    <p class="text-3xl font-bold text-orange-600">$47</p>
                                </div>
                            </div>
                        </div>

                        <!-- Enhanced Filters and Analytics -->
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 mb-8">
                            <!-- Top Row: Filters and Actions -->
                            <div class="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between mb-6">
                                <!-- Left: Advanced Filters -->
                                <div class="flex flex-wrap items-center gap-4">
                                    <div class="flex items-center gap-3">
                                        <span class="text-sm font-medium text-slate-700">Filter by:</span>
                                        <div class="flex gap-2">
                                            <button class="customer-filter-btn active px-3 py-1.5 text-xs font-medium bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors" data-filter="all">
                                                <i data-lucide="users" class="w-3 h-3 mr-1"></i>
                                                All (1,247)
                                            </button>
                                            <button class="customer-filter-btn px-3 py-1.5 text-xs font-medium bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors" data-filter="vip">
                                                <i data-lucide="crown" class="w-3 h-3 mr-1"></i>
                                                VIP (89)
                                            </button>
                                            <button class="customer-filter-btn px-3 py-1.5 text-xs font-medium bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors" data-filter="regular">
                                                <i data-lucide="user-check" class="w-3 h-3 mr-1"></i>
                                                Regular (1,156)
                                            </button>
                                            <button class="customer-filter-btn px-3 py-1.5 text-xs font-medium bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors" data-filter="new">
                                                <i data-lucide="sparkles" class="w-3 h-3 mr-1"></i>
                                                New (2)
                                            </button>
                                        </div>
                                    </div>
                                    <div class="flex items-center gap-3">
                                        <span class="text-sm font-medium text-slate-700">Visit frequency:</span>
                                        <select id="frequency-filter" class="px-3 py-1.5 text-xs font-medium border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                            <option value="all">All frequencies</option>
                                            <option value="frequent">Frequent (10+ visits)</option>
                                            <option value="occasional">Occasional (3-9 visits)</option>
                                            <option value="rare">Rare (1-2 visits)</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Right: Search and Advanced Actions -->
                                <div class="flex items-center gap-3">
                                    <div class="relative">
                                        <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400"></i>
                                        <input type="text" id="customer-search" placeholder="Search by name, email, phone..." class="pl-10 pr-4 py-2 border border-slate-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-72">
                                    </div>
                                    <button class="px-3 py-2 bg-slate-100 text-slate-600 rounded-lg hover:bg-slate-200 transition-colors" title="Advanced Search">
                                        <i data-lucide="filter" class="w-4 h-4"></i>
                                    </button>
                                    <div class="flex bg-slate-100 rounded-lg p-1">
                                        <button id="customer-grid-view" class="customer-view-toggle active px-3 py-1.5 text-xs font-medium bg-white text-slate-700 rounded-md shadow-sm">
                                            <i data-lucide="grid-3x3" class="w-4 h-4"></i>
                                        </button>
                                        <button id="customer-list-view" class="customer-view-toggle px-3 py-1.5 text-xs font-medium text-slate-500 rounded-md">
                                            <i data-lucide="list" class="w-4 h-4"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Bottom Row: Quick Stats and Sort -->
                            <div class="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between pt-4 border-t border-slate-100">
                                <!-- Left: Quick Analytics -->
                                <div class="flex items-center gap-6">
                                    <div class="flex items-center gap-2 text-sm">
                                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                        <span class="text-slate-600">12 customers online now</span>
                                    </div>
                                    <div class="flex items-center gap-2 text-sm">
                                        <i data-lucide="trending-up" class="w-4 h-4 text-green-600"></i>
                                        <span class="text-slate-600">+15% this month</span>
                                    </div>
                                    <div class="flex items-center gap-2 text-sm">
                                        <i data-lucide="calendar" class="w-4 h-4 text-blue-600"></i>
                                        <span class="text-slate-600">23 birthdays this month</span>
                                    </div>
                                </div>

                                <!-- Right: Sort and Bulk Actions -->
                                <div class="flex items-center gap-3">
                                    <div class="flex items-center gap-2">
                                        <span class="text-sm font-medium text-slate-700">Sort by:</span>
                                        <select id="customer-sort" class="px-3 py-1.5 text-xs font-medium border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                            <option value="name">Name (A-Z)</option>
                                            <option value="visits">Most Visits</option>
                                            <option value="spend">Highest Spend</option>
                                            <option value="recent">Recent Visit</option>
                                            <option value="loyalty">Loyalty Score</option>
                                        </select>
                                    </div>
                                    <button class="px-3 py-1.5 text-xs font-medium bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors">
                                        <i data-lucide="gift" class="w-3 h-3 mr-1"></i>
                                        Birthday Offers
                                    </button>
                                    <button class="px-3 py-1.5 text-xs font-medium bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors">
                                        <i data-lucide="mail" class="w-3 h-3 mr-1"></i>
                                        Send Campaign
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Customer Grid -->
                        <div id="customer-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                            <!-- Clean Customer Card 1 - VIP -->
                            <div class="customer-card bg-white rounded-lg p-4 shadow-sm border border-slate-200 hover:shadow-md transition-all duration-200 cursor-pointer relative" data-filter="vip" data-customer="1" data-visits="47" data-spend="2340">
                                <!-- Status Badge -->
                                <div class="absolute top-3 right-3">
                                    <span class="px-2.5 py-1 text-xs font-medium text-purple-700 bg-purple-100 rounded-md">
                                        VIP
                                    </span>
                                </div>

                                <!-- Header -->
                                <div class="flex items-center gap-3 mb-4 pr-12">
                                    <div class="w-2 h-2 bg-orange-400 rounded-full flex-shrink-0"></div>
                                    <div>
                                        <h3 class="font-semibold text-slate-900 text-base">John Smith</h3>
                                        <p class="text-sm text-slate-500"><EMAIL></p>
                                    </div>
                                </div>

                                <!-- Stats -->
                                <div class="grid grid-cols-2 gap-6 mb-4">
                                    <div>
                                        <p class="text-xs text-slate-500 mb-1">Total Visits</p>
                                        <p class="text-xl font-bold text-slate-900">47</p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-slate-500 mb-1">Total Spent</p>
                                        <p class="text-xl font-bold text-slate-900">$2,340</p>
                                    </div>
                                </div>

                                <!-- Details -->
                                <div class="space-y-1 mb-4 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-slate-600">Last Visit:</span>
                                        <span class="text-slate-900 font-medium">2 days ago</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-slate-600">Favorite:</span>
                                        <span class="text-slate-900 font-medium">Pasta Carbonara</span>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="flex gap-2">
                                    <button class="flex-1 px-4 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                                        View Profile
                                    </button>
                                    <button class="px-3 py-2.5 bg-slate-100 text-slate-600 rounded-lg hover:bg-slate-200 transition-colors">
                                        <i data-lucide="phone" class="w-4 h-4"></i>
                                    </button>
                                    <button class="px-3 py-2.5 bg-slate-100 text-slate-600 rounded-lg hover:bg-slate-200 transition-colors">
                                        <i data-lucide="mail" class="w-4 h-4"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Clean Customer Card 2 - Regular -->
                            <div class="customer-card bg-white rounded-lg p-4 shadow-sm border border-slate-200 hover:shadow-md transition-all duration-200 cursor-pointer relative" data-filter="regular" data-customer="2" data-visits="12" data-spend="580">
                                <!-- Status Badge -->
                                <div class="absolute top-3 right-3">
                                    <span class="px-2.5 py-1 text-xs font-medium text-green-700 bg-green-100 rounded-md">
                                        Regular
                                    </span>
                                </div>

                                <!-- Header -->
                                <div class="flex items-center gap-3 mb-4 pr-12">
                                    <div class="w-2 h-2 bg-orange-400 rounded-full flex-shrink-0"></div>
                                    <div>
                                        <h3 class="font-semibold text-slate-900 text-base">Sarah Davis</h3>
                                        <p class="text-sm text-slate-500"><EMAIL></p>
                                    </div>
                                </div>

                                <!-- Stats -->
                                <div class="grid grid-cols-2 gap-6 mb-4">
                                    <div>
                                        <p class="text-xs text-slate-500 mb-1">Total Visits</p>
                                        <p class="text-xl font-bold text-slate-900">12</p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-slate-500 mb-1">Total Spent</p>
                                        <p class="text-xl font-bold text-slate-900">$580</p>
                                    </div>
                                </div>

                                <!-- Details -->
                                <div class="space-y-1 mb-4 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-slate-600">Last Visit:</span>
                                        <span class="text-slate-900 font-medium">1 week ago</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-slate-600">Favorite:</span>
                                        <span class="text-slate-900 font-medium">Caesar Salad</span>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="flex gap-2">
                                    <button class="flex-1 px-4 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                                        View Profile
                                    </button>
                                    <button class="px-3 py-2.5 bg-slate-100 text-slate-600 rounded-lg hover:bg-slate-200 transition-colors">
                                        <i data-lucide="phone" class="w-4 h-4"></i>
                                    </button>
                                    <button class="px-3 py-2.5 bg-slate-100 text-slate-600 rounded-lg hover:bg-slate-200 transition-colors">
                                        <i data-lucide="mail" class="w-4 h-4"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Clean Customer Card 3 - New -->
                            <div class="customer-card bg-white rounded-lg p-4 shadow-sm border border-slate-200 hover:shadow-md transition-all duration-200 cursor-pointer relative" data-filter="new" data-customer="3" data-visits="2" data-spend="95">
                                <!-- Status Badge -->
                                <div class="absolute top-3 right-3">
                                    <span class="px-2.5 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded-md">
                                        New
                                    </span>
                                </div>

                                <!-- Header -->
                                <div class="flex items-center gap-3 mb-4 pr-12">
                                    <div class="w-2 h-2 bg-orange-400 rounded-full flex-shrink-0"></div>
                                    <div>
                                        <h3 class="font-semibold text-slate-900 text-base">Mike Wilson</h3>
                                        <p class="text-sm text-slate-500"><EMAIL></p>
                                    </div>
                                </div>

                                <!-- Stats -->
                                <div class="grid grid-cols-2 gap-6 mb-4">
                                    <div>
                                        <p class="text-xs text-slate-500 mb-1">Total Visits</p>
                                        <p class="text-xl font-bold text-slate-900">2</p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-slate-500 mb-1">Total Spent</p>
                                        <p class="text-xl font-bold text-slate-900">$95</p>
                                    </div>
                                </div>

                                <!-- Details -->
                                <div class="space-y-1 mb-4 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-slate-600">Last Visit:</span>
                                        <span class="text-slate-900 font-medium">Yesterday</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-slate-600">Favorite:</span>
                                        <span class="text-slate-900 font-medium">Margherita Pizza</span>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="flex gap-2">
                                    <button class="flex-1 px-4 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                                        View Profile
                                    </button>
                                    <button class="px-3 py-2.5 bg-slate-100 text-slate-600 rounded-lg hover:bg-slate-200 transition-colors">
                                        <i data-lucide="phone" class="w-4 h-4"></i>
                                    </button>
                                    <button class="px-3 py-2.5 bg-slate-100 text-slate-600 rounded-lg hover:bg-slate-200 transition-colors">
                                        <i data-lucide="mail" class="w-4 h-4"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Clean Customer Card 4 - VIP -->
                            <div class="customer-card bg-white rounded-lg p-4 shadow-sm border border-slate-200 hover:shadow-md transition-all duration-200 cursor-pointer relative" data-filter="vip" data-customer="4" data-visits="63" data-spend="3120">
                                <!-- Status Badge -->
                                <div class="absolute top-3 right-3">
                                    <span class="px-2.5 py-1 text-xs font-medium text-purple-700 bg-purple-100 rounded-md">
                                        VIP
                                    </span>
                                </div>

                                <!-- Header -->
                                <div class="flex items-center gap-3 mb-4 pr-12">
                                    <div class="w-2 h-2 bg-orange-400 rounded-full flex-shrink-0"></div>
                                    <div>
                                        <h3 class="font-semibold text-slate-900 text-base">Emily Johnson</h3>
                                        <p class="text-sm text-slate-500"><EMAIL></p>
                                    </div>
                                </div>

                                <!-- Stats -->
                                <div class="grid grid-cols-2 gap-6 mb-4">
                                    <div>
                                        <p class="text-xs text-slate-500 mb-1">Total Visits</p>
                                        <p class="text-xl font-bold text-slate-900">63</p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-slate-500 mb-1">Total Spent</p>
                                        <p class="text-xl font-bold text-slate-900">$3,120</p>
                                    </div>
                                </div>

                                <!-- Details -->
                                <div class="space-y-1 mb-4 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-slate-600">Last Visit:</span>
                                        <span class="text-slate-900 font-medium">Today</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-slate-600">Favorite:</span>
                                        <span class="text-slate-900 font-medium">Seafood Risotto</span>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="flex gap-2">
                                    <button class="flex-1 px-4 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                                        View Profile
                                    </button>
                                    <button class="px-3 py-2.5 bg-slate-100 text-slate-600 rounded-lg hover:bg-slate-200 transition-colors">
                                        <i data-lucide="phone" class="w-4 h-4"></i>
                                    </button>
                                    <button class="px-3 py-2.5 bg-slate-100 text-slate-600 rounded-lg hover:bg-slate-200 transition-colors">
                                        <i data-lucide="mail" class="w-4 h-4"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Clean Customer Card 5 - Regular -->
                            <div class="customer-card bg-white rounded-lg p-4 shadow-sm border border-slate-200 hover:shadow-md transition-all duration-200 cursor-pointer relative" data-filter="regular" data-customer="5" data-visits="18" data-spend="890">
                                <!-- Status Badge -->
                                <div class="absolute top-3 right-3">
                                    <span class="px-2.5 py-1 text-xs font-medium text-green-700 bg-green-100 rounded-md">
                                        Regular
                                    </span>
                                </div>

                                <!-- Header -->
                                <div class="flex items-center gap-3 mb-4 pr-12">
                                    <div class="w-2 h-2 bg-orange-400 rounded-full flex-shrink-0"></div>
                                    <div>
                                        <h3 class="font-semibold text-slate-900 text-base">David Brown</h3>
                                        <p class="text-sm text-slate-500"><EMAIL></p>
                                    </div>
                                </div>

                                <!-- Stats -->
                                <div class="grid grid-cols-2 gap-6 mb-4">
                                    <div>
                                        <p class="text-xs text-slate-500 mb-1">Total Visits</p>
                                        <p class="text-xl font-bold text-slate-900">18</p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-slate-500 mb-1">Total Spent</p>
                                        <p class="text-xl font-bold text-slate-900">$890</p>
                                    </div>
                                </div>

                                <!-- Details -->
                                <div class="space-y-1 mb-4 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-slate-600">Last Visit:</span>
                                        <span class="text-slate-900 font-medium">3 days ago</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-slate-600">Favorite:</span>
                                        <span class="text-slate-900 font-medium">Grilled Salmon</span>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="flex gap-2">
                                    <button class="flex-1 px-4 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                                        View Profile
                                    </button>
                                    <button class="px-3 py-2.5 bg-slate-100 text-slate-600 rounded-lg hover:bg-slate-200 transition-colors">
                                        <i data-lucide="phone" class="w-4 h-4"></i>
                                    </button>
                                    <button class="px-3 py-2.5 bg-slate-100 text-slate-600 rounded-lg hover:bg-slate-200 transition-colors">
                                        <i data-lucide="mail" class="w-4 h-4"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Clean Customer Card 6 - New -->
                            <div class="customer-card bg-white rounded-lg p-4 shadow-sm border border-slate-200 hover:shadow-md transition-all duration-200 cursor-pointer relative" data-filter="new" data-customer="6" data-visits="1" data-spend="42">
                                <!-- Status Badge -->
                                <div class="absolute top-3 right-3">
                                    <span class="px-2.5 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded-md">
                                        New
                                    </span>
                                </div>

                                <!-- Header -->
                                <div class="flex items-center gap-3 mb-4 pr-12">
                                    <div class="w-2 h-2 bg-orange-400 rounded-full flex-shrink-0"></div>
                                    <div>
                                        <h3 class="font-semibold text-slate-900 text-base">Lisa Martinez</h3>
                                        <p class="text-sm text-slate-500"><EMAIL></p>
                                    </div>
                                </div>

                                <!-- Stats -->
                                <div class="grid grid-cols-2 gap-6 mb-4">
                                    <div>
                                        <p class="text-xs text-slate-500 mb-1">Total Visits</p>
                                        <p class="text-xl font-bold text-slate-900">1</p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-slate-500 mb-1">Total Spent</p>
                                        <p class="text-xl font-bold text-slate-900">$42</p>
                                    </div>
                                </div>

                                <!-- Details -->
                                <div class="space-y-1 mb-4 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-slate-600">Last Visit:</span>
                                        <span class="text-slate-900 font-medium">3 days ago</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-slate-600">Favorite:</span>
                                        <span class="text-slate-900 font-medium">Chicken Parmesan</span>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="flex gap-2">
                                    <button class="flex-1 px-4 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                                        View Profile
                                    </button>
                                    <button class="px-3 py-2.5 bg-slate-100 text-slate-600 rounded-lg hover:bg-slate-200 transition-colors">
                                        <i data-lucide="phone" class="w-4 h-4"></i>
                                    </button>
                                    <button class="px-3 py-2.5 bg-slate-100 text-slate-600 rounded-lg hover:bg-slate-200 transition-colors">
                                        <i data-lucide="mail" class="w-4 h-4"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Clean Customer Card 7 - Regular -->
                            <div class="customer-card bg-white rounded-lg p-4 shadow-sm border border-slate-200 hover:shadow-md transition-all duration-200 cursor-pointer relative" data-filter="regular" data-customer="7" data-visits="25" data-spend="1250">
                                <!-- Status Badge -->
                                <div class="absolute top-3 right-3">
                                    <span class="px-2.5 py-1 text-xs font-medium text-green-700 bg-green-100 rounded-md">
                                        Regular
                                    </span>
                                </div>

                                <!-- Header -->
                                <div class="flex items-center gap-3 mb-4 pr-12">
                                    <div class="w-2 h-2 bg-orange-400 rounded-full flex-shrink-0"></div>
                                    <div>
                                        <h3 class="font-semibold text-slate-900 text-base">Robert Taylor</h3>
                                        <p class="text-sm text-slate-500"><EMAIL></p>
                                    </div>
                                </div>

                                <!-- Stats -->
                                <div class="grid grid-cols-2 gap-6 mb-4">
                                    <div>
                                        <p class="text-xs text-slate-500 mb-1">Total Visits</p>
                                        <p class="text-xl font-bold text-slate-900">25</p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-slate-500 mb-1">Total Spent</p>
                                        <p class="text-xl font-bold text-slate-900">$1,250</p>
                                    </div>
                                </div>

                                <!-- Details -->
                                <div class="space-y-1 mb-4 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-slate-600">Last Visit:</span>
                                        <span class="text-slate-900 font-medium">5 days ago</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-slate-600">Favorite:</span>
                                        <span class="text-slate-900 font-medium">BBQ Ribs</span>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="flex gap-2">
                                    <button class="flex-1 px-4 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                                        View Profile
                                    </button>
                                    <button class="px-3 py-2.5 bg-slate-100 text-slate-600 rounded-lg hover:bg-slate-200 transition-colors">
                                        <i data-lucide="phone" class="w-4 h-4"></i>
                                    </button>
                                    <button class="px-3 py-2.5 bg-slate-100 text-slate-600 rounded-lg hover:bg-slate-200 transition-colors">
                                        <i data-lucide="mail" class="w-4 h-4"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Clean Customer Card 8 - VIP -->
                            <div class="customer-card bg-white rounded-lg p-4 shadow-sm border border-slate-200 hover:shadow-md transition-all duration-200 cursor-pointer relative" data-filter="vip" data-customer="8" data-visits="52" data-spend="2680">
                                <!-- Status Badge -->
                                <div class="absolute top-3 right-3">
                                    <span class="px-2.5 py-1 text-xs font-medium text-purple-700 bg-purple-100 rounded-md">
                                        VIP
                                    </span>
                                </div>

                                <!-- Header -->
                                <div class="flex items-center gap-3 mb-4 pr-12">
                                    <div class="w-2 h-2 bg-orange-400 rounded-full flex-shrink-0"></div>
                                    <div>
                                        <h3 class="font-semibold text-slate-900 text-base">Anna White</h3>
                                        <p class="text-sm text-slate-500"><EMAIL></p>
                                    </div>
                                </div>

                                <!-- Stats -->
                                <div class="grid grid-cols-2 gap-6 mb-4">
                                    <div>
                                        <p class="text-xs text-slate-500 mb-1">Total Visits</p>
                                        <p class="text-xl font-bold text-slate-900">52</p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-slate-500 mb-1">Total Spent</p>
                                        <p class="text-xl font-bold text-slate-900">$2,680</p>
                                    </div>
                                </div>

                                <!-- Details -->
                                <div class="space-y-1 mb-4 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-slate-600">Last Visit:</span>
                                        <span class="text-slate-900 font-medium">1 day ago</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-slate-600">Favorite:</span>
                                        <span class="text-slate-900 font-medium">Lobster Thermidor</span>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="flex gap-2">
                                    <button class="flex-1 px-4 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                                        View Profile
                                    </button>
                                    <button class="px-3 py-2.5 bg-slate-100 text-slate-600 rounded-lg hover:bg-slate-200 transition-colors">
                                        <i data-lucide="phone" class="w-4 h-4"></i>
                                    </button>
                                    <button class="px-3 py-2.5 bg-slate-100 text-slate-600 rounded-lg hover:bg-slate-200 transition-colors">
                                        <i data-lucide="mail" class="w-4 h-4"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Customer Insights Panel -->
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 mt-8">
                            <div class="flex items-center justify-between mb-6">
                                <div>
                                    <h3 class="text-lg font-bold text-slate-900">Customer Insights</h3>
                                    <p class="text-sm text-slate-500">Analytics and trends for better customer relationships</p>
                                </div>
                                <button class="px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors text-sm font-medium">
                                    <i data-lucide="bar-chart-3" class="w-4 h-4 mr-1"></i>
                                    View Full Report
                                </button>
                            </div>

                            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                                <!-- Customer Segments -->
                                <div class="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-xl">
                                    <h4 class="font-semibold text-slate-800 mb-3 flex items-center gap-2">
                                        <i data-lucide="pie-chart" class="w-4 h-4 text-blue-600"></i>
                                        Customer Segments
                                    </h4>
                                    <div class="space-y-3">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                                                <span class="text-sm text-slate-700">VIP Members</span>
                                            </div>
                                            <span class="text-sm font-medium text-slate-900">7.1%</span>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                                <span class="text-sm text-slate-700">Regular Customers</span>
                                            </div>
                                            <span class="text-sm font-medium text-slate-900">92.7%</span>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                                                <span class="text-sm text-slate-700">New Customers</span>
                                            </div>
                                            <span class="text-sm font-medium text-slate-900">0.2%</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Popular Dishes -->
                                <div class="bg-gradient-to-br from-orange-50 to-orange-100 p-4 rounded-xl">
                                    <h4 class="font-semibold text-slate-800 mb-3 flex items-center gap-2">
                                        <i data-lucide="utensils" class="w-4 h-4 text-orange-600"></i>
                                        Popular Dishes
                                    </h4>
                                    <div class="space-y-2">
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm text-slate-700">Pasta Carbonara</span>
                                            <span class="text-xs bg-orange-200 text-orange-800 px-2 py-1 rounded-full">23%</span>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm text-slate-700">Caesar Salad</span>
                                            <span class="text-xs bg-orange-200 text-orange-800 px-2 py-1 rounded-full">18%</span>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm text-slate-700">Margherita Pizza</span>
                                            <span class="text-xs bg-orange-200 text-orange-800 px-2 py-1 rounded-full">15%</span>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm text-slate-700">Grilled Salmon</span>
                                            <span class="text-xs bg-orange-200 text-orange-800 px-2 py-1 rounded-full">12%</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Recent Activity -->
                                <div class="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-xl">
                                    <h4 class="font-semibold text-slate-800 mb-3 flex items-center gap-2">
                                        <i data-lucide="activity" class="w-4 h-4 text-green-600"></i>
                                        Recent Activity
                                    </h4>
                                    <div class="space-y-3">
                                        <div class="flex items-center gap-3">
                                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                            <div class="flex-1">
                                                <p class="text-sm text-slate-700">John Smith made a reservation</p>
                                                <p class="text-xs text-slate-500">2 minutes ago</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-3">
                                            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                            <div class="flex-1">
                                                <p class="text-sm text-slate-700">New customer registered</p>
                                                <p class="text-xs text-slate-500">15 minutes ago</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-3">
                                            <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                                            <div class="flex-1">
                                                <p class="text-sm text-slate-700">Emily Johnson reached VIP status</p>
                                                <p class="text-xs text-slate-500">1 hour ago</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Actions Bar -->
                            <div class="mt-6 pt-6 border-t border-slate-200">
                                <div class="flex flex-wrap items-center justify-between gap-4">
                                    <div class="flex items-center gap-4">
                                        <span class="text-sm font-medium text-slate-700">Quick Actions:</span>
                                        <div class="flex gap-2">
                                            <button class="px-3 py-1.5 text-xs font-medium bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors">
                                                <i data-lucide="crown" class="w-3 h-3 mr-1"></i>
                                                Upgrade to VIP
                                            </button>
                                            <button class="px-3 py-1.5 text-xs font-medium bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors">
                                                <i data-lucide="gift" class="w-3 h-3 mr-1"></i>
                                                Send Birthday Offers
                                            </button>
                                            <button class="px-3 py-1.5 text-xs font-medium bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors">
                                                <i data-lucide="mail" class="w-3 h-3 mr-1"></i>
                                                Newsletter Campaign
                                            </button>
                                        </div>
                                    </div>
                                    <div class="flex items-center gap-2 text-sm text-slate-500">
                                        <i data-lucide="clock" class="w-4 h-4"></i>
                                        <span>Last updated: 2 minutes ago</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </main>
                </div>

                <!-- Inventory Content -->
                <div id="inventory-content" class="content-section">
                    <!-- Header -->
                    <header class="flex justify-between items-center p-4 sm:p-6 bg-white border-b">
                        <div class="flex items-center">
                            <!-- Mobile Menu Button -->
                            <button id="mobile-menu-button" class="lg:hidden mr-4 text-slate-600 hover:text-slate-900">
                                <i data-lucide="menu" class="w-6 h-6"></i>
                            </button>
                            <h2 id="header-title" class="text-2xl font-semibold text-slate-800">Inventory</h2>
                        </div>
                        <div class="flex items-center space-x-4">
                            <!-- User Role Badge -->
                            <div id="user-role-badge" class="hidden lg:flex items-center gap-2 px-3 py-2 bg-blue-100 text-blue-800 rounded-lg text-sm font-medium">
                                <span id="role-icon">👨‍💼</span>
                                <span id="role-text">Admin</span>
                            </div>

                            <button class="text-slate-600 hover:text-blue-600"><i data-lucide="bell" class="w-6 h-6"></i></button>

                            <!-- User Menu -->
                            <div class="relative">
                                <button id="user-menu-btn" class="flex items-center gap-2 text-slate-600 hover:text-slate-800 transition-colors">
                                    <img id="user-avatar" class="h-10 w-10 rounded-full object-cover" src="https://placehold.co/100x100/E2E8F0/475569?text=AV" alt="User avatar">
                                    <span class="absolute right-0 bottom-0 h-3 w-3 bg-green-500 rounded-full border-2 border-white"></span>
                                    <i data-lucide="chevron-down" class="w-4 h-4"></i>
                                </button>

                                <!-- Dropdown Menu -->
                                <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-slate-200 py-2 z-50">
                                    <div class="px-4 py-2 border-b border-slate-100">
                                        <p id="user-name" class="font-semibold text-slate-800">User Name</p>
                                        <p id="user-email" class="text-sm text-slate-500"><EMAIL></p>
                                    </div>
                                    <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                        <i data-lucide="user" class="w-4 h-4"></i>
                                        Profile
                                    </a>
                                    <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                        <i data-lucide="settings" class="w-4 h-4"></i>
                                        Settings
                                    </a>
                                    <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                        <i data-lucide="help-circle" class="w-4 h-4"></i>
                                        Help
                                    </a>
                                    <hr class="my-2 border-slate-200">
                                    <button id="logout-btn" class="w-full flex items-center gap-2 px-4 py-2 text-red-600 hover:bg-red-50 transition-colors">
                                        <i data-lucide="log-out" class="w-4 h-4"></i>
                                        Logout
                                    </button>
                                </div>
                            </div>
                        </div>
                    </header>

                    <main class="flex-1 overflow-auto p-4 sm:p-6">
                        <!-- Page Header -->
                        <div class="flex flex-col sm:flex-row sm:items-center justify-between mb-8">
                            <div class="space-y-1">
                                <h1 class="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
                                    Inventory
                                </h1>
                                <p class="text-slate-500 font-medium">Smart inventory management for your restaurant</p>
                            </div>
                            <div class="flex gap-3 mt-6 sm:mt-0">
                                <button class="group px-4 py-2.5 bg-white border border-slate-200 text-slate-600 rounded-xl hover:border-slate-300 hover:shadow-sm transition-all duration-200 flex items-center gap-2 text-sm font-medium">
                                    <i data-lucide="download" class="w-4 h-4 group-hover:scale-110 transition-transform"></i>
                                    Export Data
                                </button>
                                <button class="group px-4 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-200 flex items-center gap-2 text-sm font-medium">
                                    <i data-lucide="plus" class="w-4 h-4 group-hover:rotate-90 transition-transform"></i>
                                    Add Item
                                </button>
                            </div>
                        </div>

                        <!-- Modern Stats Cards -->
                        <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                            <div class="group bg-gradient-to-br from-white to-blue-50/30 rounded-2xl p-5 border border-blue-100/50 hover:border-blue-200 hover:shadow-lg hover:shadow-blue-500/10 transition-all duration-300 cursor-pointer">
                                <div class="flex items-center gap-4">
                                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25 group-hover:scale-110 transition-transform duration-300">
                                        <i data-lucide="package" class="w-6 h-6 text-white"></i>
                                    </div>
                                    <div>
                                        <p class="text-xs font-medium text-slate-500 uppercase tracking-wide">Total Items</p>
                                        <p class="text-2xl font-bold text-slate-900 group-hover:text-blue-600 transition-colors">247</p>
                                    </div>
                                </div>
                            </div>

                            <div class="group bg-gradient-to-br from-white to-red-50/30 rounded-2xl p-5 border border-red-100/50 hover:border-red-200 hover:shadow-lg hover:shadow-red-500/10 transition-all duration-300 cursor-pointer">
                                <div class="flex items-center gap-4">
                                    <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg shadow-red-500/25 group-hover:scale-110 transition-transform duration-300">
                                        <i data-lucide="alert-triangle" class="w-6 h-6 text-white"></i>
                                    </div>
                                    <div>
                                        <p class="text-xs font-medium text-slate-500 uppercase tracking-wide">Low Stock</p>
                                        <p class="text-2xl font-bold text-slate-900 group-hover:text-red-600 transition-colors">12</p>
                                    </div>
                                </div>
                            </div>

                            <div class="group bg-gradient-to-br from-white to-amber-50/30 rounded-2xl p-5 border border-amber-100/50 hover:border-amber-200 hover:shadow-lg hover:shadow-amber-500/10 transition-all duration-300 cursor-pointer">
                                <div class="flex items-center gap-4">
                                    <div class="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center shadow-lg shadow-amber-500/25 group-hover:scale-110 transition-transform duration-300">
                                        <i data-lucide="clock" class="w-6 h-6 text-white"></i>
                                    </div>
                                    <div>
                                        <p class="text-xs font-medium text-slate-500 uppercase tracking-wide">Expiring</p>
                                        <p class="text-2xl font-bold text-slate-900 group-hover:text-amber-600 transition-colors">8</p>
                                    </div>
                                </div>
                            </div>

                            <div class="group bg-gradient-to-br from-white to-emerald-50/30 rounded-2xl p-5 border border-emerald-100/50 hover:border-emerald-200 hover:shadow-lg hover:shadow-emerald-500/10 transition-all duration-300 cursor-pointer">
                                <div class="flex items-center gap-4">
                                    <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg shadow-emerald-500/25 group-hover:scale-110 transition-transform duration-300">
                                        <i data-lucide="dollar-sign" class="w-6 h-6 text-white"></i>
                                    </div>
                                    <div>
                                        <p class="text-xs font-medium text-slate-500 uppercase tracking-wide">Total Value</p>
                                        <p class="text-2xl font-bold text-slate-900 group-hover:text-emerald-600 transition-colors">$18.4k</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Modern Search and Filters -->
                        <div class="bg-white/70 backdrop-blur-sm rounded-2xl border border-slate-200/50 p-6 mb-8 shadow-sm">
                            <div class="flex flex-col sm:flex-row gap-4">
                                <!-- Enhanced Search -->
                                <div class="relative flex-1">
                                    <div class="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400">
                                        <i data-lucide="search" class="w-5 h-5"></i>
                                    </div>
                                    <input type="text" placeholder="Search inventory items..." class="w-full pl-12 pr-4 py-3 bg-slate-50/50 border border-slate-200/50 rounded-xl focus:ring-2 focus:ring-blue-500/20 focus:border-blue-400 focus:bg-white transition-all duration-200 text-sm font-medium placeholder:text-slate-400">
                                </div>

                                <!-- Modern Filter Pills -->
                                <div class="flex gap-3">
                                    <div class="relative">
                                        <select class="appearance-none px-4 py-3 bg-slate-50/50 border border-slate-200/50 rounded-xl focus:ring-2 focus:ring-blue-500/20 focus:border-blue-400 focus:bg-white transition-all duration-200 text-sm font-medium cursor-pointer pr-10">
                                            <option value="">All Categories</option>
                                            <option value="beverages">🥤 Beverages</option>
                                            <option value="meat">🥩 Meat & Seafood</option>
                                            <option value="vegetables">🥬 Vegetables</option>
                                            <option value="dairy">🥛 Dairy</option>
                                            <option value="pantry">🍞 Pantry</option>
                                        </select>
                                        <i data-lucide="chevron-down" class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400 pointer-events-none"></i>
                                    </div>

                                    <div class="relative">
                                        <select class="appearance-none px-4 py-3 bg-slate-50/50 border border-slate-200/50 rounded-xl focus:ring-2 focus:ring-blue-500/20 focus:border-blue-400 focus:bg-white transition-all duration-200 text-sm font-medium cursor-pointer pr-10">
                                            <option value="">All Status</option>
                                            <option value="in-stock">✅ In Stock</option>
                                            <option value="low-stock">⚠️ Low Stock</option>
                                            <option value="out-of-stock">❌ Out of Stock</option>
                                            <option value="expiring">⏰ Expiring</option>
                                        </select>
                                        <i data-lucide="chevron-down" class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400 pointer-events-none"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Modern Inventory Grid -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <!-- Item 1 - Low Stock -->
                            <div class="group bg-white/80 backdrop-blur-sm rounded-2xl border border-slate-200/50 p-6 hover:border-red-200 hover:shadow-xl hover:shadow-red-500/10 transition-all duration-300 cursor-pointer">
                                <div class="flex items-start justify-between mb-4">
                                    <div class="flex items-center gap-4">
                                        <div class="w-14 h-14 bg-gradient-to-br from-orange-400 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg shadow-orange-500/25 group-hover:scale-110 transition-transform duration-300">
                                            <i data-lucide="fish" class="w-7 h-7 text-white"></i>
                                        </div>
                                        <div>
                                            <h3 class="font-semibold text-slate-900 text-lg group-hover:text-red-600 transition-colors">Salmon Fillets</h3>
                                            <p class="text-sm text-slate-500 font-medium">Fresh Atlantic</p>
                                        </div>
                                    </div>
                                    <div class="px-3 py-1.5 text-xs font-bold text-red-700 bg-gradient-to-r from-red-50 to-red-100 rounded-full border border-red-200 shadow-sm">
                                        ⚠️ Low Stock
                                    </div>
                                </div>

                                <div class="grid grid-cols-2 gap-4 mb-4">
                                    <div class="bg-slate-50/50 rounded-xl p-3">
                                        <p class="text-xs font-medium text-slate-500 uppercase tracking-wide">Current Stock</p>
                                        <p class="text-xl font-bold text-red-600 mt-1">3 lbs</p>
                                    </div>
                                    <div class="bg-slate-50/50 rounded-xl p-3">
                                        <p class="text-xs font-medium text-slate-500 uppercase tracking-wide">Min Required</p>
                                        <p class="text-xl font-bold text-slate-900 mt-1">15 lbs</p>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between text-sm text-slate-600 mb-4 p-3 bg-slate-50/30 rounded-xl">
                                    <span class="font-medium">📅 Expires: Dec 28</span>
                                    <span class="font-bold text-slate-900">$24.99/lb</span>
                                </div>

                                <button class="w-full px-4 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-200 text-sm font-bold group-hover:scale-105">
                                    🚀 Restock Now
                                </button>
                            </div>

                            <!-- Item 2 - In Stock -->
                            <div class="group bg-white/80 backdrop-blur-sm rounded-2xl border border-slate-200/50 p-6 hover:border-green-200 hover:shadow-xl hover:shadow-green-500/10 transition-all duration-300 cursor-pointer">
                                <div class="flex items-start justify-between mb-4">
                                    <div class="flex items-center gap-4">
                                        <div class="w-14 h-14 bg-gradient-to-br from-green-400 to-green-600 rounded-2xl flex items-center justify-center shadow-lg shadow-green-500/25 group-hover:scale-110 transition-transform duration-300">
                                            <i data-lucide="wheat" class="w-7 h-7 text-white"></i>
                                        </div>
                                        <div>
                                            <h3 class="font-semibold text-slate-900 text-lg group-hover:text-green-600 transition-colors">Pasta Penne</h3>
                                            <p class="text-sm text-slate-500 font-medium">Italian Durum</p>
                                        </div>
                                    </div>
                                    <div class="px-3 py-1.5 text-xs font-bold text-green-700 bg-gradient-to-r from-green-50 to-green-100 rounded-full border border-green-200 shadow-sm">
                                        ✅ In Stock
                                    </div>
                                </div>

                                <div class="grid grid-cols-2 gap-4 mb-4">
                                    <div class="bg-slate-50/50 rounded-xl p-3">
                                        <p class="text-xs font-medium text-slate-500 uppercase tracking-wide">Current Stock</p>
                                        <p class="text-xl font-bold text-green-600 mt-1">45 lbs</p>
                                    </div>
                                    <div class="bg-slate-50/50 rounded-xl p-3">
                                        <p class="text-xs font-medium text-slate-500 uppercase tracking-wide">Min Required</p>
                                        <p class="text-xl font-bold text-slate-900 mt-1">20 lbs</p>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between text-sm text-slate-600 mb-4 p-3 bg-slate-50/30 rounded-xl">
                                    <span class="font-medium">📅 Expires: Mar 15</span>
                                    <span class="font-bold text-slate-900">$3.99/lb</span>
                                </div>

                                <button class="w-full px-4 py-3 bg-gradient-to-r from-slate-100 to-slate-200 text-slate-700 rounded-xl hover:from-slate-200 hover:to-slate-300 hover:shadow-md transition-all duration-200 text-sm font-bold group-hover:scale-105">
                                    👁️ View Details
                                </button>
                            </div>

                            <!-- Item 3 - Expiring Soon -->
                            <div class="group bg-white/80 backdrop-blur-sm rounded-2xl border border-slate-200/50 p-6 hover:border-amber-200 hover:shadow-xl hover:shadow-amber-500/10 transition-all duration-300 cursor-pointer">
                                <div class="flex items-start justify-between mb-4">
                                    <div class="flex items-center gap-4">
                                        <div class="w-14 h-14 bg-gradient-to-br from-amber-400 to-amber-600 rounded-2xl flex items-center justify-center shadow-lg shadow-amber-500/25 group-hover:scale-110 transition-transform duration-300">
                                            <i data-lucide="milk" class="w-7 h-7 text-white"></i>
                                        </div>
                                        <div>
                                            <h3 class="font-semibold text-slate-900 text-lg group-hover:text-amber-600 transition-colors">Heavy Cream</h3>
                                            <p class="text-sm text-slate-500 font-medium">Organic Dairy</p>
                                        </div>
                                    </div>
                                    <div class="px-3 py-1.5 text-xs font-bold text-amber-700 bg-gradient-to-r from-amber-50 to-amber-100 rounded-full border border-amber-200 shadow-sm">
                                        ⏰ Expiring
                                    </div>
                                </div>

                                <div class="grid grid-cols-2 gap-4 mb-4">
                                    <div class="bg-slate-50/50 rounded-xl p-3">
                                        <p class="text-xs font-medium text-slate-500 uppercase tracking-wide">Current Stock</p>
                                        <p class="text-xl font-bold text-amber-600 mt-1">8 qt</p>
                                    </div>
                                    <div class="bg-slate-50/50 rounded-xl p-3">
                                        <p class="text-xs font-medium text-slate-500 uppercase tracking-wide">Min Required</p>
                                        <p class="text-xl font-bold text-slate-900 mt-1">5 qt</p>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between text-sm text-slate-600 mb-4 p-3 bg-amber-50/50 rounded-xl border border-amber-100">
                                    <span class="font-bold text-amber-700">⚡ Expires: Dec 26</span>
                                    <span class="font-bold text-slate-900">$4.99/qt</span>
                                </div>

                                <button class="w-full px-4 py-3 bg-gradient-to-r from-amber-500 to-amber-600 text-white rounded-xl hover:from-amber-600 hover:to-amber-700 hover:shadow-lg hover:shadow-amber-500/25 transition-all duration-200 text-sm font-bold group-hover:scale-105">
                                    ⚡ Use First
                                </button>
                            </div>

                            <!-- Item 4 - Out of Stock -->
                            <div class="group bg-white/80 backdrop-blur-sm rounded-2xl border border-slate-200/50 p-6 hover:border-red-200 hover:shadow-xl hover:shadow-red-500/10 transition-all duration-300 cursor-pointer">
                                <div class="flex items-start justify-between mb-4">
                                    <div class="flex items-center gap-4">
                                        <div class="w-14 h-14 bg-gradient-to-br from-purple-400 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg shadow-purple-500/25 group-hover:scale-110 transition-transform duration-300">
                                            <i data-lucide="wine" class="w-7 h-7 text-white"></i>
                                        </div>
                                        <div>
                                            <h3 class="font-semibold text-slate-900 text-lg group-hover:text-red-600 transition-colors">Red Wine</h3>
                                            <p class="text-sm text-slate-500 font-medium">Cabernet Sauvignon</p>
                                        </div>
                                    </div>
                                    <div class="px-3 py-1.5 text-xs font-bold text-red-700 bg-gradient-to-r from-red-50 to-red-100 rounded-full border border-red-200 shadow-sm">
                                        ❌ Out of Stock
                                    </div>
                                </div>

                                <div class="grid grid-cols-2 gap-4 mb-4">
                                    <div class="bg-slate-50/50 rounded-xl p-3">
                                        <p class="text-xs font-medium text-slate-500 uppercase tracking-wide">Current Stock</p>
                                        <p class="text-xl font-bold text-red-600 mt-1">0 bottles</p>
                                    </div>
                                    <div class="bg-slate-50/50 rounded-xl p-3">
                                        <p class="text-xs font-medium text-slate-500 uppercase tracking-wide">Min Required</p>
                                        <p class="text-xl font-bold text-slate-900 mt-1">12 bottles</p>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between text-sm text-slate-600 mb-4 p-3 bg-red-50/30 rounded-xl border border-red-100">
                                    <span class="font-medium">📦 Last order: Dec 15</span>
                                    <span class="font-bold text-slate-900">$28.99/bottle</span>
                                </div>

                                <button class="w-full px-4 py-3 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-xl hover:from-red-700 hover:to-red-800 hover:shadow-lg hover:shadow-red-500/25 transition-all duration-200 text-sm font-bold group-hover:scale-105">
                                    🛒 Order Now
                                </button>
                            </div>

                            <!-- Item 5 - In Stock -->
                            <div class="group bg-white/80 backdrop-blur-sm rounded-2xl border border-slate-200/50 p-6 hover:border-emerald-200 hover:shadow-xl hover:shadow-emerald-500/10 transition-all duration-300 cursor-pointer">
                                <div class="flex items-start justify-between mb-4">
                                    <div class="flex items-center gap-4">
                                        <div class="w-14 h-14 bg-gradient-to-br from-red-400 to-red-600 rounded-2xl flex items-center justify-center shadow-lg shadow-red-500/25 group-hover:scale-110 transition-transform duration-300">
                                            <i data-lucide="cherry" class="w-7 h-7 text-white"></i>
                                        </div>
                                        <div>
                                            <h3 class="font-semibold text-slate-900 text-lg group-hover:text-emerald-600 transition-colors">Fresh Tomatoes</h3>
                                            <p class="text-sm text-slate-500 font-medium">Organic Roma</p>
                                        </div>
                                    </div>
                                    <div class="px-3 py-1.5 text-xs font-bold text-emerald-700 bg-gradient-to-r from-emerald-50 to-emerald-100 rounded-full border border-emerald-200 shadow-sm">
                                        ✅ In Stock
                                    </div>
                                </div>

                                <div class="grid grid-cols-2 gap-4 mb-4">
                                    <div class="bg-slate-50/50 rounded-xl p-3">
                                        <p class="text-xs font-medium text-slate-500 uppercase tracking-wide">Current Stock</p>
                                        <p class="text-xl font-bold text-emerald-600 mt-1">25 lbs</p>
                                    </div>
                                    <div class="bg-slate-50/50 rounded-xl p-3">
                                        <p class="text-xs font-medium text-slate-500 uppercase tracking-wide">Min Required</p>
                                        <p class="text-xl font-bold text-slate-900 mt-1">10 lbs</p>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between text-sm text-slate-600 mb-4 p-3 bg-slate-50/30 rounded-xl">
                                    <span class="font-medium">📅 Expires: Dec 30</span>
                                    <span class="font-bold text-slate-900">$2.99/lb</span>
                                </div>

                                <button class="w-full px-4 py-3 bg-gradient-to-r from-slate-100 to-slate-200 text-slate-700 rounded-xl hover:from-slate-200 hover:to-slate-300 hover:shadow-md transition-all duration-200 text-sm font-bold group-hover:scale-105">
                                    👁️ View Details
                                </button>
                            </div>

                            <!-- Item 6 - Low Stock -->
                            <div class="group bg-white/80 backdrop-blur-sm rounded-2xl border border-slate-200/50 p-6 hover:border-blue-200 hover:shadow-xl hover:shadow-blue-500/10 transition-all duration-300 cursor-pointer">
                                <div class="flex items-start justify-between mb-4">
                                    <div class="flex items-center gap-4">
                                        <div class="w-14 h-14 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-2xl flex items-center justify-center shadow-lg shadow-yellow-500/25 group-hover:scale-110 transition-transform duration-300">
                                            <i data-lucide="droplets" class="w-7 h-7 text-white"></i>
                                        </div>
                                        <div>
                                            <h3 class="font-semibold text-slate-900 text-lg group-hover:text-blue-600 transition-colors">Olive Oil</h3>
                                            <p class="text-sm text-slate-500 font-medium">Extra Virgin</p>
                                        </div>
                                    </div>
                                    <div class="px-3 py-1.5 text-xs font-bold text-red-700 bg-gradient-to-r from-red-50 to-red-100 rounded-full border border-red-200 shadow-sm">
                                        ⚠️ Low Stock
                                    </div>
                                </div>

                                <div class="grid grid-cols-2 gap-4 mb-4">
                                    <div class="bg-slate-50/50 rounded-xl p-3">
                                        <p class="text-xs font-medium text-slate-500 uppercase tracking-wide">Current Stock</p>
                                        <p class="text-xl font-bold text-red-600 mt-1">2 bottles</p>
                                    </div>
                                    <div class="bg-slate-50/50 rounded-xl p-3">
                                        <p class="text-xs font-medium text-slate-500 uppercase tracking-wide">Min Required</p>
                                        <p class="text-xl font-bold text-slate-900 mt-1">8 bottles</p>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between text-sm text-slate-600 mb-4 p-3 bg-slate-50/30 rounded-xl">
                                    <span class="font-medium">📅 Expires: Jun 15</span>
                                    <span class="font-bold text-slate-900">$12.99/bottle</span>
                                </div>

                                <button class="w-full px-4 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-200 text-sm font-bold group-hover:scale-105">
                                    🚀 Restock Now
                                </button>
                            </div>
                        </div>
                    </main>
                </div>

                <div id="analytics-content" class="content-section">
                    <!-- Header -->
                    <header class="flex justify-between items-center p-4 sm:p-6 bg-white border-b">
                        <div class="flex items-center">
                            <!-- Mobile Menu Button -->
                            <button id="mobile-menu-button" class="lg:hidden mr-4 text-slate-600 hover:text-slate-900">
                                <i data-lucide="menu" class="w-6 h-6"></i>
                            </button>
                            <h2 id="header-title" class="text-2xl font-semibold text-slate-800">Analytics</h2>
                        </div>
                        <div class="flex items-center space-x-4">
                            <!-- User Role Badge -->
                            <div id="user-role-badge" class="hidden lg:flex items-center gap-2 px-3 py-2 bg-blue-100 text-blue-800 rounded-lg text-sm font-medium">
                                <span id="role-icon">👨‍💼</span>
                                <span id="role-text">Admin</span>
                            </div>

                            <button class="text-slate-600 hover:text-blue-600"><i data-lucide="bell" class="w-6 h-6"></i></button>

                            <!-- User Menu -->
                            <div class="relative">
                                <button id="user-menu-btn" class="flex items-center gap-2 text-slate-600 hover:text-slate-800 transition-colors">
                                    <img id="user-avatar" class="h-10 w-10 rounded-full object-cover" src="https://placehold.co/100x100/E2E8F0/475569?text=AV" alt="User avatar">
                                    <span class="absolute right-0 bottom-0 h-3 w-3 bg-green-500 rounded-full border-2 border-white"></span>
                                    <i data-lucide="chevron-down" class="w-4 h-4"></i>
                                </button>

                                <!-- Dropdown Menu -->
                                <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-slate-200 py-2 z-50">
                                    <div class="px-4 py-2 border-b border-slate-100">
                                        <p id="user-name" class="font-semibold text-slate-800">User Name</p>
                                        <p id="user-email" class="text-sm text-slate-500"><EMAIL></p>
                                    </div>
                                    <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                        <i data-lucide="user" class="w-4 h-4"></i>
                                        Profile
                                    </a>
                                    <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                        <i data-lucide="settings" class="w-4 h-4"></i>
                                        Settings
                                    </a>
                                    <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                        <i data-lucide="help-circle" class="w-4 h-4"></i>
                                        Help
                                    </a>
                                    <hr class="my-2 border-slate-200">
                                    <button id="logout-btn" class="w-full flex items-center gap-2 px-4 py-2 text-red-600 hover:bg-red-50 transition-colors">
                                        <i data-lucide="log-out" class="w-4 h-4"></i>
                                        Logout
                                    </button>
                                </div>
                            </div>
                        </div>
                    </header>
                    <main class="flex-1 overflow-auto p-4 sm:p-6">
                        <p class="p-6">Analytics charts will be here.</p>
                    </main>
                </div>

                <!-- Settings Content -->
                <div id="settings-content" class="content-section">
                    <!-- Header -->
                    <header class="flex justify-between items-center p-4 sm:p-6 bg-white border-b">
                        <div class="flex items-center">
                            <!-- Mobile Menu Button -->
                            <button id="mobile-menu-button" class="lg:hidden mr-4 text-slate-600 hover:text-slate-900">
                                <i data-lucide="menu" class="w-6 h-6"></i>
                            </button>
                            <h2 id="header-title" class="text-2xl font-semibold text-slate-800">Settings</h2>
                        </div>
                        <div class="flex items-center space-x-4">
                            <!-- User Role Badge -->
                            <div id="user-role-badge" class="hidden lg:flex items-center gap-2 px-3 py-2 bg-blue-100 text-blue-800 rounded-lg text-sm font-medium">
                                <span id="role-icon">👨‍💼</span>
                                <span id="role-text">Admin</span>
                            </div>

                            <button class="text-slate-600 hover:text-blue-600"><i data-lucide="bell" class="w-6 h-6"></i></button>

                            <!-- User Menu -->
                            <div class="relative">
                                <button id="user-menu-btn" class="flex items-center gap-2 text-slate-600 hover:text-slate-800 transition-colors">
                                    <img id="user-avatar" class="h-10 w-10 rounded-full object-cover" src="https://placehold.co/100x100/E2E8F0/475569?text=AV" alt="User avatar">
                                    <span class="absolute right-0 bottom-0 h-3 w-3 bg-green-500 rounded-full border-2 border-white"></span>
                                    <i data-lucide="chevron-down" class="w-4 h-4"></i>
                                </button>

                                <!-- Dropdown Menu -->
                                <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-slate-200 py-2 z-50">
                                    <div class="px-4 py-2 border-b border-slate-100">
                                        <p id="user-name" class="font-semibold text-slate-800">User Name</p>
                                        <p id="user-email" class="text-sm text-slate-500"><EMAIL></p>
                                    </div>
                                    <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                        <i data-lucide="user" class="w-4 h-4"></i>
                                        Profile
                                    </a>
                                    <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                        <i data-lucide="settings" class="w-4 h-4"></i>
                                        Settings
                                    </a>
                                    <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                        <i data-lucide="help-circle" class="w-4 h-4"></i>
                                        Help
                                    </a>
                                    <hr class="my-2 border-slate-200">
                                    <button id="logout-btn" class="w-full flex items-center gap-2 px-4 py-2 text-red-600 hover:bg-red-50 transition-colors">
                                        <i data-lucide="log-out" class="w-4 h-4"></i>
                                        Logout
                                    </button>
                                </div>
                            </div>
                        </div>
                    </header>
                    <main class="flex-1 overflow-auto p-4 sm:p-6">
                        <p class="p-6">Settings page will be here.</p>
                    </main>
                </div>
            </main>
        </div>
    </div>

    <!-- Add Task Modal -->
    <div id="add-task-modal" class="fixed inset-0 bg-black bg-opacity-50 z-40 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md m-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-semibold text-slate-800">Add a New Task</h3>
                <button id="close-modal-btn" class="text-slate-500 hover:text-slate-800">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>
            <form id="add-task-form">
                <div class="space-y-4">
                    <div>
                        <label for="task-title" class="block text-sm font-medium text-slate-700">Title</label>
                        <input type="text" id="task-title" name="title" required class="mt-1 block w-full px-3 py-2 border border-slate-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label for="task-description" class="block text-sm font-medium text-slate-700">Description</label>
                        <textarea id="task-description" name="description" rows="3" class="mt-1 block w-full px-3 py-2 border border-slate-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
                    </div>
                    <div>
                        <label for="task-category" class="block text-sm font-medium text-slate-700">Category</label>
                        <input type="text" id="task-category" name="category" required class="mt-1 block w-full px-3 py-2 border border-slate-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="e.g., Cleaning, Inventory">
                    </div>
                </div>
                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" id="cancel-modal-btn" class="px-4 py-2 text-sm font-medium text-slate-700 bg-slate-100 rounded-md hover:bg-slate-200">Cancel</button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">Save Task</button>
                </div>
            </form>
        </div>
    </div>


    <script>
        // User interface setup function
        function setupUserInterface(userData) {
            const roleIcons = {
                admin: '👨‍💼',
                manager: '👩‍💼',
                staff: '👨‍🍳',
                waiter: '🧑‍🍳'
            };

            const roleNames = {
                admin: 'Admin',
                manager: 'Manager',
                staff: 'Staff',
                waiter: 'Waiter'
            };

            // Update role badge
            const roleIcon = document.getElementById('role-icon');
            const roleText = document.getElementById('role-text');
            const roleBadge = document.getElementById('user-role-badge');

            if (roleIcon && roleText && userData.role) {
                roleIcon.textContent = roleIcons[userData.role] || '👤';
                roleText.textContent = roleNames[userData.role] || userData.role;
                roleBadge.classList.remove('hidden');
            }

            // Update user menu
            const userNameEl = document.querySelector('#user-menu #user-name');
            const userEmailEl = document.querySelector('#user-menu #user-email');
            const userAvatar = document.getElementById('user-avatar');

            if (userNameEl) userNameEl.textContent = userData.name || 'User';
            if (userEmailEl) userEmailEl.textContent = userData.email || '';

            // Update avatar with role-based initials
            if (userAvatar) {
                const initials = (userData.name || userData.role || 'U').substring(0, 2).toUpperCase();
                userAvatar.src = `https://placehold.co/100x100/2563eb/ffffff?text=${initials}`;
            }

            // Setup user menu toggle
            const userMenuBtn = document.getElementById('user-menu-btn');
            const userMenu = document.getElementById('user-menu');

            if (userMenuBtn && userMenu) {
                userMenuBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    userMenu.classList.toggle('hidden');
                });

                // Close menu when clicking outside
                document.addEventListener('click', () => {
                    userMenu.classList.add('hidden');
                });

                userMenu.addEventListener('click', (e) => {
                    e.stopPropagation();
                });
            }

            // Setup logout functionality
            const logoutBtn = document.getElementById('logout-btn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', () => {
                    // Show confirmation dialog
                    const confirmed = confirm('Are you sure you want to logout?');
                    if (confirmed) {
                        // Clear user session
                        const userData = JSON.parse(localStorage.getItem('restroManagerUser') || '{}');
                        userData.isLoggedIn = false;
                        localStorage.setItem('restroManagerUser', JSON.stringify(userData));

                        // Show logout message and redirect
                        const logoutModal = document.createElement('div');
                        logoutModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                        logoutModal.innerHTML = `
                            <div class="bg-white p-8 rounded-2xl shadow-2xl max-w-md mx-4 text-center">
                                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i data-lucide="log-out" class="w-8 h-8 text-blue-600"></i>
                                </div>
                                <h3 class="text-2xl font-bold text-slate-800 mb-2">Logged Out</h3>
                                <p class="text-slate-600 mb-6">You have been successfully logged out. Redirecting to login page...</p>
                            </div>
                        `;

                        document.body.appendChild(logoutModal);
                        lucide.createIcons();

                        setTimeout(() => {
                            window.location.href = './login.html';
                        }, 2000);
                    }
                });
            }
        }

        // Simple Order Management Functions
        function setupOrderManagement() {
            const orderFilterBtns = document.querySelectorAll('.order-filter-btn');
            const orderRows = document.querySelectorAll('.order-row');
            const orderSearch = document.getElementById('order-search');

            // Order filter functionality
            orderFilterBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    // Remove active class from all buttons
                    orderFilterBtns.forEach(b => {
                        b.classList.remove('active', 'bg-blue-600', 'text-white');
                        b.classList.add('bg-slate-100', 'text-slate-600');
                    });

                    // Add active class to clicked button
                    btn.classList.add('active', 'bg-blue-600', 'text-white');
                    btn.classList.remove('bg-slate-100', 'text-slate-600');

                    const status = btn.dataset.status;
                    filterOrders(status);
                });
            });

            // Order search functionality
            if (orderSearch) {
                orderSearch.addEventListener('input', (e) => {
                    const searchTerm = e.target.value.toLowerCase();
                    searchOrders(searchTerm);
                });
            }
        }

        function filterOrders(status) {
            const orderRows = document.querySelectorAll('.order-row');

            orderRows.forEach(row => {
                if (status === 'all' || row.dataset.status === status) {
                    row.style.display = 'table-row';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function searchOrders(searchTerm) {
            const orderRows = document.querySelectorAll('.order-row');

            orderRows.forEach(row => {
                const orderText = row.textContent.toLowerCase();
                if (orderText.includes(searchTerm)) {
                    row.style.display = 'table-row';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // Simple order status update functions
        function updateOrderStatus(button, newStatus) {
            const row = button.closest('.order-row');
            const statusBadge = row.querySelector('.status-badge');

            if (newStatus === 'preparing') {
                statusBadge.innerHTML = '<i data-lucide="chef-hat" class="w-3 h-3"></i> Preparing';
                statusBadge.className = 'status-badge';
                statusBadge.style.background = 'var(--primary-50)';
                statusBadge.style.color = 'var(--primary-600)';
                row.dataset.status = 'preparing';
                button.textContent = 'Mark Ready';
                button.onclick = () => updateOrderStatus(button, 'ready');
            } else if (newStatus === 'ready') {
                statusBadge.innerHTML = '<i data-lucide="check-circle" class="w-3 h-3"></i> Ready';
                statusBadge.className = 'status-badge status-active';
                row.dataset.status = 'ready';
                button.textContent = 'Serve';
                button.onclick = () => updateOrderStatus(button, 'completed');
            } else if (newStatus === 'completed') {
                statusBadge.innerHTML = '<i data-lucide="check" class="w-3 h-3"></i> Completed';
                statusBadge.className = 'status-badge status-completed';
                row.dataset.status = 'completed';
                button.style.display = 'none';
            }

            lucide.createIcons();
        }

        function rejectOrder(button) {
            if (confirm('Are you sure you want to reject this order?')) {
                const row = button.closest('.order-row');
                row.style.display = 'none';
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            // Initialize Lucide Icons
            lucide.createIcons();

            // Enhanced Order Management
            setupOrderManagement();

            // Check authentication and load user data
            const userData = JSON.parse(localStorage.getItem('restroManagerUser') || '{}');

            // Redirect to login if not authenticated
            if (!userData.email || !userData.isLoggedIn) {
                window.location.href = './login.html';
                return;
            }

            // Setup user interface
            setupUserInterface(userData);

            // Check for new user and show welcome banner
            const welcomeBanner = document.getElementById('welcome-banner');
            const userNameSpan = document.getElementById('user-name');
            const dismissWelcome = document.getElementById('dismiss-welcome');

            if (userData.isNewUser && userData.firstName) {
                welcomeBanner.classList.remove('hidden');
                userNameSpan.textContent = userData.firstName;

                // Auto-hide welcome banner after 10 seconds
                setTimeout(() => {
                    if (welcomeBanner && !welcomeBanner.classList.contains('hidden')) {
                        welcomeBanner.style.transition = 'opacity 0.5s ease-out';
                        welcomeBanner.style.opacity = '0';
                        setTimeout(() => {
                            welcomeBanner.classList.add('hidden');
                            // Mark user as no longer new
                            userData.isNewUser = false;
                            localStorage.setItem('restroManagerUser', JSON.stringify(userData));
                        }, 500);
                    }
                }, 10000);
            }

            // Manual dismiss welcome banner
            if (dismissWelcome) {
                dismissWelcome.addEventListener('click', () => {
                    welcomeBanner.style.transition = 'opacity 0.3s ease-out';
                    welcomeBanner.style.opacity = '0';
                    setTimeout(() => {
                        welcomeBanner.classList.add('hidden');
                        // Mark user as no longer new
                        userData.isNewUser = false;
                        localStorage.setItem('restroManagerUser', JSON.stringify(userData));
                    }, 300);
                });
            }

            // --- DOM Elements ---
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const sidebarBackdrop = document.getElementById('sidebar-backdrop');
            
            const sidebarLinks = document.querySelectorAll('.sidebar-item');
            const contentSections = document.querySelectorAll('.content-section');
            const headerTitle = document.getElementById('header-title');
            
            const menuFilterButtonsContainer = document.getElementById('menu-filter-buttons');
            const menuSearchInput = document.getElementById('menu-search-input');
            const menuItems = document.querySelectorAll('.menu-item-card');

            // Task Modal Elements
            const addTaskBtn = document.getElementById('add-task-btn');
            const addTaskModal = document.getElementById('add-task-modal');
            const closeModalBtn = document.getElementById('close-modal-btn');
            const cancelModalBtn = document.getElementById('cancel-modal-btn');
            const addTaskForm = document.getElementById('add-task-form');
            const todoTasksContainer = document.getElementById('todo-tasks');

            const SIDEBAR_STATE_KEY = 'sidebarCollapsed';

            // --- Sidebar Logic ---
            const applySidebarState = (isCollapsed) => {
                sidebar.classList.toggle('collapsed', isCollapsed);
            };

            const toggleDesktopSidebar = () => {
                const isCollapsed = !sidebar.classList.contains('collapsed');
                applySidebarState(isCollapsed);
                localStorage.setItem(SIDEBAR_STATE_KEY, isCollapsed.toString());
            };

            if(sidebarToggle) sidebarToggle.addEventListener('click', toggleDesktopSidebar);

            const toggleMobileSidebar = () => {
                sidebar.classList.toggle('-translate-x-full');
                sidebarBackdrop.classList.toggle('hidden');
            };

            if(mobileMenuButton) mobileMenuButton.addEventListener('click', toggleMobileSidebar);
            if(sidebarBackdrop) sidebarBackdrop.addEventListener('click', toggleMobileSidebar);

            const initSidebar = () => {
                const isDesktop = window.innerWidth >= 1024;
                if (isDesktop) {
                    const savedState = localStorage.getItem(SIDEBAR_STATE_KEY) === 'true';
                    applySidebarState(savedState);
                    sidebar.classList.remove('-translate-x-full');
                    sidebarBackdrop.classList.add('hidden');
                } else {
                    sidebar.classList.add('-translate-x-full');
                    sidebar.classList.remove('collapsed');
                    sidebarBackdrop.classList.add('hidden');
                }
            };


            
            // --- Navigation Logic ---
            sidebarLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    const targetId = e.currentTarget.dataset.target;
                    const targetContent = document.getElementById(targetId + '-content');
                    const linkText = e.currentTarget.querySelector('.sidebar-link-text').textContent;

                    sidebarLinks.forEach(l => l.classList.remove('active'));
                    e.currentTarget.classList.add('active');

                    headerTitle.textContent = linkText;

                    contentSections.forEach(section => section.classList.remove('active'));
                    if(targetContent) {
                        targetContent.classList.add('active');

                        // Initialize section-specific functionality
                        if (targetId === 'tables') {
                            setTimeout(() => {
                                initTableManagement();
                            }, 100);
                        } else if (targetId === 'customers') {
                            setTimeout(() => {
                                initCustomerManagement();
                            }, 100);
                        }
                    }

                    if (window.innerWidth < 1024) {
                        toggleMobileSidebar();
                    }
                });
            });

            // --- Menu Page Logic ---
            const filterMenuItems = () => {
                if (!menuSearchInput || !menuFilterButtonsContainer) return;
                const searchTerm = menuSearchInput.value.toLowerCase();
                const activeFilter = menuFilterButtonsContainer.querySelector('.filter-btn.active').dataset.filter;

                menuItems.forEach(item => {
                    const category = item.dataset.category;
                    const name = item.dataset.name.toLowerCase();
                    const categoryMatch = activeFilter === 'all' || category === activeFilter;
                    const searchMatch = name.includes(searchTerm);
                    item.style.display = (categoryMatch && searchMatch) ? 'block' : 'none';
                });
            };

            if (menuFilterButtonsContainer) {
                menuFilterButtonsContainer.addEventListener('click', (e) => {
                    if (e.target.matches('.filter-btn')) {
                        const currentActive = menuFilterButtonsContainer.querySelector('.filter-btn.active');
                        if (currentActive) {
                            currentActive.classList.remove('active', 'bg-blue-600', 'text-white');
                            currentActive.classList.add('text-slate-600', 'bg-slate-100');
                        }
                        e.target.classList.add('active', 'bg-blue-600', 'text-white');
                        e.target.classList.remove('text-slate-600', 'bg-slate-100');
                        filterMenuItems();
                    }
                });
            }

            if (menuSearchInput) {
                menuSearchInput.addEventListener('input', filterMenuItems);
            }

            // --- Task Board Logic ---
            const openTaskModal = () => addTaskModal.classList.remove('hidden');
            const closeTaskModal = () => addTaskModal.classList.add('hidden');

            if (addTaskBtn) addTaskBtn.addEventListener('click', openTaskModal);
            if (closeModalBtn) closeModalBtn.addEventListener('click', closeTaskModal);
            if (cancelModalBtn) cancelModalBtn.addEventListener('click', closeTaskModal);

            if (addTaskForm) {
                addTaskForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    const title = e.target.title.value;
                    const description = e.target.description.value;
                    const category = e.target.category.value;
                    
                    if (!title || !category) return;

                    const newTaskId = 'task-' + Date.now();
                    const newTaskCard = document.createElement('div');
                    newTaskCard.id = newTaskId;
                    newTaskCard.className = 'task-card bg-slate-50 p-4 rounded-lg border border-slate-200 cursor-pointer';
                    newTaskCard.setAttribute('draggable', 'true');
                    newTaskCard.innerHTML = `
                        <h5 class="font-semibold text-slate-800 mb-1">${title}</h5>
                        <p class="text-sm text-slate-600 mb-3">${description}</p>
                        <div class="flex justify-between items-center">
                            <div class="flex -space-x-2">
                                <!-- Placeholder for assignees -->
                            </div>
                            <span class="px-2 py-1 text-xs font-medium text-gray-800 bg-gray-100 rounded-full">${category}</span>
                        </div>
                    `;
                    
                    todoTasksContainer.appendChild(newTaskCard);
                    addTaskToDragEvents(newTaskCard);
                    
                    e.target.reset();
                    closeTaskModal();
                });
            }

            // Drag and Drop Logic
            const taskCards = document.querySelectorAll('.task-card');
            const taskContainers = document.querySelectorAll('.tasks-container');
            let draggedItem = null;

            function addTaskToDragEvents(task) {
                task.addEventListener('dragstart', () => {
                    draggedItem = task;
                    setTimeout(() => task.classList.add('dragging'), 0);
                });

                task.addEventListener('dragend', () => {
                    draggedItem.classList.remove('dragging');
                    draggedItem = null;
                });
            }

            taskCards.forEach(task => {
                addTaskToDragEvents(task);
            });

            taskContainers.forEach(container => {
                container.addEventListener('dragover', e => {
                    e.preventDefault();
                    container.classList.add('drag-over');
                });

                container.addEventListener('dragleave', () => {
                    container.classList.remove('drag-over');
                });

                container.addEventListener('drop', e => {
                    e.preventDefault();
                    container.classList.remove('drag-over');
                    if (draggedItem) {
                        container.appendChild(draggedItem);
                    }
                });
            });

            // --- Orders Management Functionality ---
            function initOrdersManagement() {
                // Order filter functionality
                const filterButtons = document.querySelectorAll('.order-filter-btn');
                const orderRows = document.querySelectorAll('[data-status]');

                filterButtons.forEach(button => {
                    button.addEventListener('click', () => {
                        // Remove active class from all buttons
                        filterButtons.forEach(btn => {
                            btn.classList.remove('active', 'bg-blue-600', 'text-white');
                            btn.classList.add('bg-slate-100', 'text-slate-700');
                        });

                        // Add active class to clicked button
                        button.classList.add('active', 'bg-blue-600', 'text-white');
                        button.classList.remove('bg-slate-100', 'text-slate-700');

                        const filterStatus = button.getAttribute('data-status');

                        // Filter order rows
                        orderRows.forEach(row => {
                            if (filterStatus === 'all' || row.getAttribute('data-status') === filterStatus) {
                                row.style.display = '';
                            } else {
                                row.style.display = 'none';
                            }
                        });

                        // Update showing count
                        const visibleRows = Array.from(orderRows).filter(row => row.style.display !== 'none');
                        const countElement = document.querySelector('.bg-slate-50 .text-sm.text-slate-600');
                        if (countElement) {
                            countElement.innerHTML = `Showing 1-${visibleRows.length} of ${visibleRows.length} orders`;
                        }
                    });
                });

                // Search functionality
                const searchInput = document.getElementById('order-search');
                if (searchInput) {
                    searchInput.addEventListener('input', (e) => {
                        const searchTerm = e.target.value.toLowerCase();

                        orderRows.forEach(row => {
                            const orderText = row.textContent.toLowerCase();
                            if (orderText.includes(searchTerm)) {
                                row.style.display = '';
                            } else {
                                row.style.display = 'none';
                            }
                        });

                        // Update count after search
                        const visibleRows = Array.from(orderRows).filter(row => row.style.display !== 'none');
                        const countElement = document.querySelector('.bg-slate-50 .text-sm.text-slate-600');
                        if (countElement) {
                            countElement.innerHTML = `Showing 1-${visibleRows.length} of ${visibleRows.length} orders`;
                        }
                    });
                }

                // Order action buttons
                function setupOrderActions() {
                    // Accept buttons
                    document.querySelectorAll('button').forEach(button => {
                        if (button.textContent.trim() === 'Accept') {
                            button.addEventListener('click', (e) => {
                                e.preventDefault();
                                const row = button.closest('tr');
                                const orderId = row.querySelector('td:first-child .font-medium').textContent;

                                // Update status
                                const statusBadge = row.querySelector('.inline-flex');
                                statusBadge.className = 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800';
                                statusBadge.textContent = 'Kitchen';
                                row.setAttribute('data-status', 'preparing');

                                // Update action buttons
                                button.parentElement.innerHTML = `
                                    <button class="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700">
                                        Mark Ready
                                    </button>
                                `;

                                // Show success message
                                showNotification(`Order ${orderId} accepted and sent to kitchen!`, 'success');

                                // Re-setup actions for new buttons
                                setTimeout(setupOrderActions, 100);
                            });
                        }
                    });

                    // Reject buttons
                    document.querySelectorAll('button').forEach(button => {
                        if (button.textContent.trim() === 'Reject') {
                            button.addEventListener('click', (e) => {
                                e.preventDefault();
                                const row = button.closest('tr');
                                const orderId = row.querySelector('td:first-child .font-medium').textContent;

                                if (confirm(`Are you sure you want to reject order ${orderId}?`)) {
                                    row.style.opacity = '0.5';
                                    row.style.textDecoration = 'line-through';

                                    // Update status
                                    const statusBadge = row.querySelector('.inline-flex');
                                    statusBadge.className = 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800';
                                    statusBadge.textContent = 'Rejected';

                                    // Remove action buttons
                                    button.parentElement.innerHTML = '<span class="text-xs text-red-500">Rejected</span>';

                                    showNotification(`Order ${orderId} has been rejected.`, 'error');
                                }
                            });
                        }
                    });

                    // Mark Ready buttons
                    document.querySelectorAll('button').forEach(button => {
                        if (button.textContent.trim() === 'Mark Ready') {
                            button.addEventListener('click', (e) => {
                                e.preventDefault();
                                const row = button.closest('tr');
                                const orderId = row.querySelector('td:first-child .font-medium').textContent;

                                // Update status
                                const statusBadge = row.querySelector('.inline-flex');
                                statusBadge.className = 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800';
                                statusBadge.textContent = 'Ready';
                                row.setAttribute('data-status', 'ready');

                                // Update action button
                                button.outerHTML = `
                                    <button class="px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700">
                                        Serve
                                    </button>
                                `;

                                showNotification(`Order ${orderId} is ready to serve!`, 'success');

                                // Re-setup actions for new buttons
                                setTimeout(setupOrderActions, 100);
                            });
                        }
                    });

                    // Serve buttons
                    document.querySelectorAll('button').forEach(button => {
                        if (button.textContent.trim() === 'Serve') {
                            button.addEventListener('click', (e) => {
                                e.preventDefault();
                                const row = button.closest('tr');
                                const orderId = row.querySelector('td:first-child .font-medium').textContent;

                                // Update status
                                const statusBadge = row.querySelector('.inline-flex');
                                statusBadge.className = 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800';
                                statusBadge.textContent = 'Completed';
                                row.setAttribute('data-status', 'completed');

                                // Update action button
                                button.outerHTML = '<span class="text-xs text-green-600">✓ Served</span>';

                                showNotification(`Order ${orderId} has been served successfully!`, 'success');
                            });
                        }
                    });
                }

                // New Order button
                const newOrderBtn = Array.from(document.querySelectorAll('button')).find(btn => btn.textContent.includes('New Order'));
                if (newOrderBtn) {
                    newOrderBtn.addEventListener('click', () => {
                        showNotification('New Order feature coming soon!', 'info');
                    });
                }

                // Export button
                const exportBtn = Array.from(document.querySelectorAll('button')).find(btn => btn.textContent.includes('Export'));
                if (exportBtn) {
                    exportBtn.addEventListener('click', () => {
                        showNotification('Exporting orders data...', 'info');
                        // Simulate export
                        setTimeout(() => {
                            showNotification('Orders exported successfully!', 'success');
                        }, 2000);
                    });
                }

                // Refresh button
                const refreshBtn = Array.from(document.querySelectorAll('button')).find(btn => btn.textContent.includes('Refresh'));
                if (refreshBtn) {
                    refreshBtn.addEventListener('click', () => {
                        showNotification('Refreshing orders...', 'info');
                        // Simulate refresh
                        setTimeout(() => {
                            location.reload();
                        }, 1000);
                    });
                }

                // Setup initial actions
                setupOrderActions();
            }

            // Notification system
            function showNotification(message, type = 'info') {
                // Remove existing notifications
                const existingNotification = document.querySelector('.notification');
                if (existingNotification) {
                    existingNotification.remove();
                }

                // Create notification
                const notification = document.createElement('div');
                notification.className = `notification fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

                // Set colors based on type
                const colors = {
                    success: 'bg-green-600 text-white',
                    error: 'bg-red-600 text-white',
                    info: 'bg-blue-600 text-white',
                    warning: 'bg-yellow-600 text-white'
                };

                notification.className += ` ${colors[type] || colors.info}`;
                notification.textContent = message;

                document.body.appendChild(notification);

                // Animate in
                setTimeout(() => {
                    notification.classList.remove('translate-x-full');
                }, 100);

                // Auto remove after 3 seconds
                setTimeout(() => {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.remove();
                        }
                    }, 300);
                }, 3000);
            }

            // --- Task Management Functionality ---
            function initTaskManagement() {
                // Drag and Drop functionality
                const taskCards = document.querySelectorAll('.task-card');
                const taskContainers = document.querySelectorAll('.tasks-container');
                let draggedItem = null;

                function addTaskToDragEvents(task) {
                    task.addEventListener('dragstart', (e) => {
                        draggedItem = task;
                        task.classList.add('opacity-50', 'transform', 'rotate-2');
                        e.dataTransfer.effectAllowed = 'move';
                    });

                    task.addEventListener('dragend', () => {
                        task.classList.remove('opacity-50', 'transform', 'rotate-2');
                        draggedItem = null;
                    });
                }

                taskCards.forEach(task => {
                    addTaskToDragEvents(task);
                });

                taskContainers.forEach(container => {
                    container.addEventListener('dragover', (e) => {
                        e.preventDefault();
                        container.classList.add('bg-blue-50', 'border-blue-300');
                        e.dataTransfer.dropEffect = 'move';
                    });

                    container.addEventListener('dragleave', () => {
                        container.classList.remove('bg-blue-50', 'border-blue-300');
                    });

                    container.addEventListener('drop', (e) => {
                        e.preventDefault();
                        container.classList.remove('bg-blue-50', 'border-blue-300');

                        if (draggedItem) {
                            // Find the add button and insert before it
                            const addButton = container.querySelector('button');
                            if (addButton) {
                                container.insertBefore(draggedItem, addButton);
                            } else {
                                container.appendChild(draggedItem);
                            }

                            // Update task status based on column
                            updateTaskStatus(draggedItem, container.id);
                            showNotification('Task moved successfully!', 'success');
                        }
                    });
                });

                // Update task status and appearance based on column
                function updateTaskStatus(taskElement, containerId) {
                    const statusBadge = taskElement.querySelector('.text-xs.font-medium');
                    const progressBar = taskElement.querySelector('.bg-purple-600');

                    switch(containerId) {
                        case 'todo-tasks':
                            if (statusBadge) {
                                statusBadge.className = 'text-xs font-medium text-orange-600 bg-orange-100 px-2 py-1 rounded-full';
                                statusBadge.textContent = 'To Do';
                            }
                            // Remove progress bar if exists
                            if (progressBar) {
                                progressBar.closest('.mb-4').remove();
                            }
                            break;
                        case 'inprogress-tasks':
                            if (statusBadge) {
                                statusBadge.className = 'text-xs font-medium text-purple-600 bg-purple-100 px-2 py-1 rounded-full';
                                statusBadge.textContent = 'In Progress';
                            }
                            // Add progress bar if doesn't exist
                            if (!progressBar) {
                                addProgressBar(taskElement);
                            }
                            break;
                        case 'done-tasks':
                            if (statusBadge) {
                                statusBadge.className = 'text-xs font-medium text-green-600 bg-green-100 px-2 py-1 rounded-full';
                                statusBadge.textContent = '✓ Completed';
                            }
                            // Remove progress bar and add completion info
                            if (progressBar) {
                                progressBar.closest('.mb-4').remove();
                            }
                            addCompletionInfo(taskElement);
                            break;
                    }
                }

                function addProgressBar(taskElement) {
                    const progressHTML = `
                        <div class="mb-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-xs font-medium text-slate-600">Progress</span>
                                <span class="text-xs text-slate-500">25%</span>
                            </div>
                            <div class="w-full bg-slate-200 rounded-full h-2">
                                <div class="bg-purple-600 h-2 rounded-full" style="width: 25%"></div>
                            </div>
                        </div>
                    `;
                    const description = taskElement.querySelector('p');
                    description.insertAdjacentHTML('afterend', progressHTML);
                }

                function addCompletionInfo(taskElement) {
                    const existingCompletion = taskElement.querySelector('.bg-green-50');
                    if (!existingCompletion) {
                        const completionHTML = `
                            <div class="mb-4 p-3 bg-green-50 rounded-lg border border-green-200">
                                <div class="flex items-center gap-2 text-sm">
                                    <i data-lucide="check-circle" class="w-4 h-4 text-green-600"></i>
                                    <span class="text-green-800 font-medium">Completed just now</span>
                                </div>
                            </div>
                        `;
                        const description = taskElement.querySelector('p');
                        description.insertAdjacentHTML('afterend', completionHTML);

                        // Re-initialize lucide icons
                        if (window.lucide) {
                            window.lucide.createIcons();
                        }
                    }
                }

                // Add Task functionality
                const addTaskBtns = document.querySelectorAll('#add-task-btn, button:contains("Add new task")');
                addTaskBtns.forEach(btn => {
                    if (btn.textContent.includes('Add')) {
                        btn.addEventListener('click', () => {
                            showAddTaskModal();
                        });
                    }
                });

                function showAddTaskModal() {
                    const modal = createTaskModal();
                    document.body.appendChild(modal);

                    // Focus on title input
                    setTimeout(() => {
                        modal.querySelector('#task-title').focus();
                    }, 100);
                }

                function createTaskModal() {
                    const modal = document.createElement('div');
                    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
                    modal.innerHTML = `
                        <div class="bg-white rounded-2xl p-6 w-full max-w-md shadow-2xl">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-xl font-bold text-slate-900">Add New Task</h3>
                                <button id="close-modal" class="p-2 hover:bg-slate-100 rounded-lg transition-colors">
                                    <i data-lucide="x" class="w-5 h-5 text-slate-500"></i>
                                </button>
                            </div>

                            <form id="add-task-form" class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">Task Title</label>
                                    <input type="text" id="task-title" class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500" placeholder="Enter task title..." required>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">Description</label>
                                    <textarea id="task-description" rows="3" class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500" placeholder="Enter task description..."></textarea>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">Priority</label>
                                    <select id="task-priority" class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                                        <option value="low">Low Priority</option>
                                        <option value="medium" selected>Medium Priority</option>
                                        <option value="high">High Priority</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">Category</label>
                                    <select id="task-category" class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                                        <option value="cleaning">Cleaning</option>
                                        <option value="inventory">Inventory</option>
                                        <option value="kitchen">Kitchen Prep</option>
                                        <option value="setup">Setup</option>
                                        <option value="service">Service</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">Estimated Time (minutes)</label>
                                    <input type="number" id="task-time" class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500" placeholder="30" min="5" max="480">
                                </div>

                                <div class="flex gap-3 pt-4">
                                    <button type="button" id="cancel-task" class="flex-1 px-4 py-2 border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 transition-colors">
                                        Cancel
                                    </button>
                                    <button type="submit" class="flex-1 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                                        Add Task
                                    </button>
                                </div>
                            </form>
                        </div>
                    `;

                    // Add event listeners
                    modal.querySelector('#close-modal').addEventListener('click', () => {
                        modal.remove();
                    });

                    modal.querySelector('#cancel-task').addEventListener('click', () => {
                        modal.remove();
                    });

                    modal.addEventListener('click', (e) => {
                        if (e.target === modal) {
                            modal.remove();
                        }
                    });

                    modal.querySelector('#add-task-form').addEventListener('submit', (e) => {
                        e.preventDefault();
                        createNewTask(modal);
                    });

                    // Re-initialize lucide icons
                    setTimeout(() => {
                        if (window.lucide) {
                            window.lucide.createIcons();
                        }
                    }, 100);

                    return modal;
                }

                function createNewTask(modal) {
                    const title = modal.querySelector('#task-title').value;
                    const description = modal.querySelector('#task-description').value;
                    const priority = modal.querySelector('#task-priority').value;
                    const category = modal.querySelector('#task-category').value;
                    const time = modal.querySelector('#task-time').value || '30';

                    const priorityColors = {
                        low: { bg: 'bg-green-50', text: 'text-green-600', dot: 'bg-green-500', border: 'border-green-200' },
                        medium: { bg: 'bg-yellow-50', text: 'text-yellow-600', dot: 'bg-yellow-500', border: 'border-yellow-200' },
                        high: { bg: 'bg-orange-50', text: 'text-orange-600', dot: 'bg-orange-500', border: 'border-orange-200' }
                    };

                    const categoryColors = {
                        cleaning: { bg: 'bg-blue-50', text: 'text-blue-700', border: 'border-blue-200' },
                        inventory: { bg: 'bg-indigo-50', text: 'text-indigo-700', border: 'border-indigo-200' },
                        kitchen: { bg: 'bg-amber-50', text: 'text-amber-700', border: 'border-amber-200' },
                        setup: { bg: 'bg-green-50', text: 'text-green-700', border: 'border-green-200' },
                        service: { bg: 'bg-purple-50', text: 'text-purple-700', border: 'border-purple-200' }
                    };

                    const taskId = 'task-' + Date.now();
                    const taskHTML = `
                        <div id="${taskId}" class="task-card bg-white p-4 cursor-pointer hover:bg-slate-50 transition-all duration-200 group rounded-lg border border-slate-200 shadow-sm" draggable="true" data-status="todo">
                            <div class="flex items-center gap-4">
                                <!-- Left: Status & Priority -->
                                <div class="flex items-center gap-3 flex-shrink-0">
                                    <div class="w-1 h-6 bg-orange-500 rounded-full"></div>
                                    <span class="text-xs font-semibold text-orange-600 bg-orange-50 px-2.5 py-1 rounded-md border border-orange-200">TO DO</span>
                                    <span class="text-xs font-semibold ${priorityColors[priority].text} ${priorityColors[priority].bg} px-2.5 py-1 rounded-md border ${priorityColors[priority].border}">${priority.toUpperCase()}</span>
                                </div>

                                <!-- Center: Task Content -->
                                <div class="flex-1 min-w-0">
                                    <h5 class="font-semibold text-slate-900 mb-1 truncate">${title}</h5>
                                    <p class="text-sm text-slate-600 line-clamp-1">${description}</p>
                                </div>

                                <!-- Right: Metadata -->
                                <div class="flex items-center gap-4 flex-shrink-0">
                                    <!-- Assignees -->
                                    <div class="flex -space-x-1">
                                        <img class="h-6 w-6 rounded-full object-cover border-2 border-white shadow-sm" src="https://placehold.co/100x100/E0F2FE/0891B2?text=ME" alt="Current User">
                                    </div>

                                    <!-- Time & Category -->
                                    <div class="flex items-center gap-3">
                                        <div class="flex items-center gap-1 text-xs text-slate-500 bg-slate-50 px-2 py-1 rounded border">
                                            <i data-lucide="clock" class="w-3 h-3"></i>
                                            <span>${time}m</span>
                                        </div>
                                        <span class="px-2.5 py-1 text-xs font-medium ${categoryColors[category].text} ${categoryColors[category].bg} rounded-md border ${categoryColors[category].border}">${category.charAt(0).toUpperCase() + category.slice(1)}</span>
                                    </div>

                                    <!-- Actions -->
                                    <button class="opacity-0 group-hover:opacity-100 p-1.5 hover:bg-slate-100 rounded-lg transition-all">
                                        <i data-lucide="more-horizontal" class="w-4 h-4 text-slate-400"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;

                    // Add to task list
                    const allTasksContainer = document.getElementById('all-tasks');
                    const addButton = allTasksContainer.querySelector('button');
                    addButton.insertAdjacentHTML('beforebegin', taskHTML);

                    // Add drag events to new task
                    const newTask = document.getElementById(taskId);
                    addTaskToDragEvents(newTask);

                    // Re-initialize lucide icons
                    if (window.lucide) {
                        window.lucide.createIcons();
                    }

                    // Update statistics
                    updateTaskStatistics();

                    modal.remove();
                    showNotification('Task created successfully!', 'success');
                }

                function updateTaskStatistics() {
                    const todoTasks = document.querySelectorAll('#todo-tasks .task-card').length;
                    const inProgressTasks = document.querySelectorAll('#inprogress-tasks .task-card').length;
                    const doneTasks = document.querySelectorAll('#done-tasks .task-card').length;
                    const totalTasks = todoTasks + inProgressTasks + doneTasks;

                    // Update header counts
                    document.querySelector('#todo-tasks').previousElementSibling.querySelector('span').textContent = todoTasks;
                    document.querySelector('#inprogress-tasks').previousElementSibling.querySelector('span').textContent = inProgressTasks;
                    document.querySelector('#done-tasks').previousElementSibling.querySelector('span').textContent = doneTasks;

                    // Update statistics cards
                    const statsCards = document.querySelectorAll('.grid.grid-cols-1.sm\\:grid-cols-2.lg\\:grid-cols-4 .text-3xl.font-bold');
                    if (statsCards[0]) statsCards[0].textContent = totalTasks;
                    if (statsCards[1]) statsCards[1].textContent = todoTasks;
                    if (statsCards[2]) statsCards[2].textContent = inProgressTasks;
                    if (statsCards[3]) statsCards[3].textContent = doneTasks;
                }

                // Initialize statistics
                updateTaskStatistics();
            }

            // --- Table Management Functionality ---
            function initTableManagement() {
                // Only initialize if tables section exists
                const tablesSection = document.getElementById('tables-content');
                if (!tablesSection) return;

                // Filter functionality
                const filterButtons = document.querySelectorAll('.table-filter-btn');
                const tableCards = document.querySelectorAll('.table-card');

                console.log('Found filter buttons:', filterButtons.length);
                console.log('Found table cards:', tableCards.length);

                filterButtons.forEach(button => {
                    button.addEventListener('click', () => {
                        // Remove active class from all buttons
                        filterButtons.forEach(btn => {
                            btn.classList.remove('active', 'bg-slate-600', 'text-white');
                            btn.classList.add('bg-slate-100', 'text-slate-700');
                        });

                        // Add active class to clicked button
                        button.classList.add('active', 'bg-slate-600', 'text-white');
                        button.classList.remove('bg-slate-100', 'text-slate-700');

                        const filterStatus = button.getAttribute('data-status');

                        // Filter table cards
                        tableCards.forEach(card => {
                            if (filterStatus === 'all' || card.getAttribute('data-status') === filterStatus) {
                                card.style.display = '';
                            } else {
                                card.style.display = 'none';
                            }
                        });

                        // Update statistics
                        updateTableStatistics();
                    });
                });

                // Search functionality
                const searchInput = document.getElementById('table-search');
                if (searchInput) {
                    searchInput.addEventListener('input', (e) => {
                        const searchTerm = e.target.value.toLowerCase();

                        tableCards.forEach(card => {
                            const tableText = card.textContent.toLowerCase();
                            if (tableText.includes(searchTerm)) {
                                card.style.display = '';
                            } else {
                                card.style.display = 'none';
                            }
                        });
                    });
                }

                // View toggle functionality
                const gridViewBtn = document.getElementById('grid-view-btn');
                const listViewBtn = document.getElementById('list-view-btn');
                const tableGrid = document.getElementById('table-grid');

                if (gridViewBtn && listViewBtn) {
                    gridViewBtn.addEventListener('click', () => {
                        // Switch to grid view
                        gridViewBtn.classList.add('active', 'bg-white', 'text-slate-700', 'shadow-sm');
                        gridViewBtn.classList.remove('text-slate-500');
                        listViewBtn.classList.remove('active', 'bg-white', 'text-slate-700', 'shadow-sm');
                        listViewBtn.classList.add('text-slate-500');

                        tableGrid.className = 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6';
                    });

                    listViewBtn.addEventListener('click', () => {
                        // Switch to list view
                        listViewBtn.classList.add('active', 'bg-white', 'text-slate-700', 'shadow-sm');
                        listViewBtn.classList.remove('text-slate-500');
                        gridViewBtn.classList.remove('active', 'bg-white', 'text-slate-700', 'shadow-sm');
                        gridViewBtn.classList.add('text-slate-500');

                        tableGrid.className = 'grid grid-cols-1 gap-4';
                    });
                }

                // Table card interactions
                tableCards.forEach(card => {
                    const tableNumber = card.getAttribute('data-table');
                    const status = card.getAttribute('data-status');

                    // Add click handler for table cards
                    card.addEventListener('click', (e) => {
                        if (!e.target.closest('button')) {
                            showTableDetails(tableNumber, status);
                        }
                    });

                    // Button interactions
                    const actionButton = card.querySelector('button');
                    if (actionButton) {
                        actionButton.addEventListener('click', (e) => {
                            e.stopPropagation();
                            handleTableAction(tableNumber, status, actionButton.textContent.trim());
                        });
                    }
                });

                // Add Table button
                const addTableBtn = document.getElementById('add-table-btn');
                if (addTableBtn) {
                    addTableBtn.addEventListener('click', () => {
                        showAddTableModal();
                    });
                }

                function showTableDetails(tableNumber, status) {
                    showNotification(`Viewing details for Table ${tableNumber} (${status})`, 'info');
                    // Here you would typically open a detailed modal
                }

                function handleTableAction(tableNumber, status, action) {
                    switch(action) {
                        case 'Reserve Table':
                            showNotification(`Table ${tableNumber} reserved successfully!`, 'success');
                            // Update table status to reserved
                            updateTableStatus(tableNumber, 'reserved');
                            break;
                        case 'Check Bill':
                            showNotification(`Opening bill for Table ${tableNumber}`, 'info');
                            break;
                        case 'View Reservation':
                            showNotification(`Viewing reservation details for Table ${tableNumber}`, 'info');
                            break;
                        case 'Mark Clean':
                            showNotification(`Table ${tableNumber} marked as clean!`, 'success');
                            updateTableStatus(tableNumber, 'available');
                            break;
                    }
                }

                function updateTableStatus(tableNumber, newStatus) {
                    const tableCard = document.querySelector(`[data-table="${tableNumber}"]`);
                    if (tableCard) {
                        tableCard.setAttribute('data-status', newStatus);

                        // Update visual elements
                        const statusBadge = tableCard.querySelector('.rounded-full');
                        const icon = tableCard.querySelector('.w-10.h-10 i');
                        const button = tableCard.querySelector('button');

                        const statusConfig = {
                            available: {
                                badge: 'px-2.5 py-1 text-xs font-medium text-green-700 bg-green-100 rounded-full',
                                text: 'Available',
                                iconClass: 'w-5 h-5 text-green-600',
                                iconName: 'check-circle',
                                buttonClass: 'w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium',
                                buttonText: 'Reserve Table'
                            },
                            occupied: {
                                badge: 'px-2.5 py-1 text-xs font-medium text-red-700 bg-red-100 rounded-full',
                                text: 'Occupied',
                                iconClass: 'w-5 h-5 text-red-600',
                                iconName: 'users',
                                buttonClass: 'w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-medium',
                                buttonText: 'Check Bill'
                            },
                            reserved: {
                                badge: 'px-2.5 py-1 text-xs font-medium text-yellow-700 bg-yellow-100 rounded-full',
                                text: 'Reserved',
                                iconClass: 'w-5 h-5 text-yellow-600',
                                iconName: 'calendar-clock',
                                buttonClass: 'w-full px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors text-sm font-medium',
                                buttonText: 'View Reservation'
                            }
                        };

                        const config = statusConfig[newStatus];
                        if (config) {
                            statusBadge.className = config.badge;
                            statusBadge.textContent = config.text;
                            button.className = config.buttonClass;
                            button.textContent = config.buttonText;
                        }

                        updateTableStatistics();
                    }
                }

                function showAddTableModal() {
                    showNotification('Add Table feature coming soon!', 'info');
                }

                function updateTableStatistics() {
                    const visibleCards = Array.from(tableCards).filter(card => card.style.display !== 'none');
                    const available = visibleCards.filter(card => card.getAttribute('data-status') === 'available').length;
                    const occupied = visibleCards.filter(card => card.getAttribute('data-status') === 'occupied').length;
                    const reserved = visibleCards.filter(card => card.getAttribute('data-status') === 'reserved').length;
                    const total = available + occupied + reserved;
                    const occupancyRate = total > 0 ? Math.round(((occupied + reserved) / total) * 100) : 0;

                    // Update statistics cards
                    const statsCards = document.querySelectorAll('.grid.grid-cols-1.sm\\:grid-cols-2.lg\\:grid-cols-4 .text-3xl.font-bold');
                    if (statsCards[0]) statsCards[0].textContent = available;
                    if (statsCards[1]) statsCards[1].textContent = occupied;
                    if (statsCards[2]) statsCards[2].textContent = reserved;
                    if (statsCards[3]) statsCards[3].textContent = occupancyRate + '%';
                }

                // Initialize statistics
                updateTableStatistics();
            }

            // --- Customer Management Functionality ---
            function initCustomerManagement() {
                // Only initialize if customers section exists
                const customersSection = document.getElementById('customers-content');
                if (!customersSection) return;

                // Filter functionality
                const filterButtons = document.querySelectorAll('.customer-filter-btn');
                const customerCards = document.querySelectorAll('.customer-card');

                console.log('Found customer filter buttons:', filterButtons.length);
                console.log('Found customer cards:', customerCards.length);

                filterButtons.forEach(button => {
                    button.addEventListener('click', () => {
                        // Remove active class from all buttons
                        filterButtons.forEach(btn => {
                            btn.classList.remove('active', 'bg-slate-600', 'text-white');
                            btn.classList.add('bg-slate-100', 'text-slate-700');
                        });

                        // Add active class to clicked button
                        button.classList.add('active', 'bg-slate-600', 'text-white');
                        button.classList.remove('bg-slate-100', 'text-slate-700');

                        const filterType = button.getAttribute('data-filter');

                        // Filter customer cards
                        customerCards.forEach(card => {
                            if (filterType === 'all' || card.getAttribute('data-filter') === filterType) {
                                card.style.display = '';
                            } else {
                                card.style.display = 'none';
                            }
                        });

                        // Update statistics
                        updateCustomerStatistics();
                    });
                });

                // Search functionality
                const searchInput = document.getElementById('customer-search');
                if (searchInput) {
                    searchInput.addEventListener('input', (e) => {
                        const searchTerm = e.target.value.toLowerCase();

                        customerCards.forEach(card => {
                            const customerText = card.textContent.toLowerCase();
                            if (customerText.includes(searchTerm)) {
                                card.style.display = '';
                            } else {
                                card.style.display = 'none';
                            }
                        });

                        // Update count after search
                        updateCustomerStatistics();
                    });
                }

                // View toggle functionality
                const gridViewBtn = document.getElementById('customer-grid-view');
                const listViewBtn = document.getElementById('customer-list-view');
                const customerGrid = document.getElementById('customer-grid');

                if (gridViewBtn && listViewBtn) {
                    gridViewBtn.addEventListener('click', () => {
                        // Switch to grid view
                        gridViewBtn.classList.add('active', 'bg-white', 'text-slate-700', 'shadow-sm');
                        gridViewBtn.classList.remove('text-slate-500');
                        listViewBtn.classList.remove('active', 'bg-white', 'text-slate-700', 'shadow-sm');
                        listViewBtn.classList.add('text-slate-500');

                        customerGrid.className = 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6';
                    });

                    listViewBtn.addEventListener('click', () => {
                        // Switch to list view
                        listViewBtn.classList.add('active', 'bg-white', 'text-slate-700', 'shadow-sm');
                        listViewBtn.classList.remove('text-slate-500');
                        gridViewBtn.classList.remove('active', 'bg-white', 'text-slate-700', 'shadow-sm');
                        gridViewBtn.classList.add('text-slate-500');

                        customerGrid.className = 'grid grid-cols-1 gap-4';
                    });
                }

                // Customer card interactions
                customerCards.forEach(card => {
                    const customerId = card.getAttribute('data-customer');
                    const customerType = card.getAttribute('data-filter');

                    // Add click handler for customer cards
                    card.addEventListener('click', (e) => {
                        if (!e.target.closest('button')) {
                            showCustomerDetails(customerId, customerType);
                        }
                    });

                    // Button interactions
                    const buttons = card.querySelectorAll('button');
                    const viewProfileBtn = buttons[0];
                    const phoneBtn = buttons[1];
                    const emailBtn = buttons[2];

                    if (viewProfileBtn) {
                        viewProfileBtn.addEventListener('click', (e) => {
                            e.stopPropagation();
                            showCustomerProfile(customerId);
                        });
                    }

                    if (phoneBtn) {
                        phoneBtn.addEventListener('click', (e) => {
                            e.stopPropagation();
                            handleCustomerCall(customerId);
                        });
                    }

                    if (emailBtn) {
                        emailBtn.addEventListener('click', (e) => {
                            e.stopPropagation();
                            handleCustomerEmail(customerId);
                        });
                    }
                });

                // Add Customer button
                const addCustomerBtn = document.getElementById('add-customer-btn');
                if (addCustomerBtn) {
                    addCustomerBtn.addEventListener('click', () => {
                        showAddCustomerModal();
                    });
                }

                function showCustomerDetails(customerId, customerType) {
                    showNotification(`Viewing details for Customer ${customerId} (${customerType})`, 'info');
                }

                function showCustomerProfile(customerId) {
                    showNotification(`Opening profile for Customer ${customerId}`, 'info');
                }

                function handleCustomerCall(customerId) {
                    showNotification(`Calling Customer ${customerId}...`, 'info');
                }

                function handleCustomerEmail(customerId) {
                    showNotification(`Opening email for Customer ${customerId}`, 'info');
                }

                function showAddCustomerModal() {
                    showNotification('Add Customer feature coming soon!', 'info');
                }

                function updateCustomerStatistics() {
                    const visibleCards = Array.from(customerCards).filter(card => card.style.display !== 'none');
                    const vipCustomers = visibleCards.filter(card => card.getAttribute('data-filter') === 'vip').length;
                    const regularCustomers = visibleCards.filter(card => card.getAttribute('data-filter') === 'regular').length;
                    const newCustomers = visibleCards.filter(card => card.getAttribute('data-filter') === 'new').length;
                    const totalCustomers = visibleCards.length;

                    // Update statistics cards if they exist
                    const customerStatsSection = document.querySelector('#customers-content .grid.grid-cols-1.sm\\:grid-cols-2.lg\\:grid-cols-4');
                    if (customerStatsSection) {
                        const statsCards = customerStatsSection.querySelectorAll('.text-3xl.font-bold');
                        if (statsCards[0]) statsCards[0].textContent = totalCustomers;
                        if (statsCards[1]) statsCards[1].textContent = vipCustomers;
                        if (statsCards[2]) statsCards[2].textContent = regularCustomers + newCustomers;
                        if (statsCards[3]) statsCards[3].textContent = '$47';
                    }
                }

                // Initialize statistics
                updateCustomerStatistics();
            }

            // --- Initializations ---
            initSidebar();
            initOrdersManagement();
            initTaskManagement();
            // initTableManagement(); // This will be called when tables section is activated
            window.addEventListener('resize', initSidebar);
        });
    </script>
</body>
</html>
