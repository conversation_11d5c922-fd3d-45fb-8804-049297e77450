<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Placeholder Icons</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            text-align: center;
        }
        .btn {
            background: #2563eb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .btn:hover {
            background: #1d4ed8;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 8px;
        }
        .success {
            background: #dcfce7;
            color: #166534;
        }
        .error {
            background: #fef2f2;
            color: #dc2626;
        }
    </style>
</head>
<body>
    <h1>🚀 Quick PWA Icon Setup</h1>
    <p>This will create placeholder icons so your PWA works immediately!</p>
    
    <button class="btn" onclick="createAllIcons()">Create All Icons Now</button>
    <button class="btn" onclick="createBasicIcons()">Create Basic Icons (Faster)</button>
    
    <div id="status"></div>
    
    <div id="preview" style="display: none;">
        <h3>Preview:</h3>
        <canvas id="preview-canvas" width="192" height="192" style="border: 1px solid #ccc; border-radius: 8px;"></canvas>
    </div>

    <script>
        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#3b82f6');
            gradient.addColorStop(1, '#2563eb');
            
            // Draw rounded rectangle background
            const radius = size * 0.15;
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, radius);
            ctx.fill();
            
            // Draw chef hat
            ctx.fillStyle = '#ffffff';
            const centerX = size / 2;
            const centerY = size / 2;
            const hatSize = size * 0.4;
            
            // Hat base
            ctx.fillRect(centerX - hatSize * 0.6, centerY + hatSize * 0.3, hatSize * 1.2, hatSize * 0.2);
            
            // Hat top
            ctx.beginPath();
            ctx.ellipse(centerX, centerY - hatSize * 0.1, hatSize * 0.6, hatSize * 0.5, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // Add "RM" text
            ctx.fillStyle = '#ffffff';
            ctx.font = `bold ${size * 0.15}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('RM', centerX, centerY + hatSize * 0.7);
            
            return canvas;
        }
        
        function downloadCanvas(canvas, filename) {
            canvas.toBlob((blob) => {
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(link.href);
            }, 'image/png');
        }
        
        function createAllIcons() {
            const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
            const status = document.getElementById('status');
            const preview = document.getElementById('preview');
            const previewCanvas = document.getElementById('preview-canvas');
            
            status.innerHTML = '<div class="status">Creating icons...</div>';
            
            // Show preview
            const previewIcon = createIcon(192);
            const previewCtx = previewCanvas.getContext('2d');
            previewCtx.drawImage(previewIcon, 0, 0);
            preview.style.display = 'block';
            
            let completed = 0;
            
            sizes.forEach((size, index) => {
                setTimeout(() => {
                    const canvas = createIcon(size);
                    downloadCanvas(canvas, `icon-${size}x${size}.png`);
                    completed++;
                    
                    status.innerHTML = `<div class="status">Downloaded ${completed}/${sizes.length} icons...</div>`;
                    
                    if (completed === sizes.length) {
                        status.innerHTML = `
                            <div class="status success">
                                ✅ All icons created successfully!<br>
                                📁 Save them in the <code>icons/</code> folder<br>
                                🚀 Then start your PWA server
                            </div>
                        `;
                    }
                }, index * 300);
            });
        }
        
        function createBasicIcons() {
            const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
            const status = document.getElementById('status');

            status.innerHTML = '<div class="status">Creating basic icons...</div>';

            let completed = 0;

            sizes.forEach((size, index) => {
                setTimeout(() => {
                    // Create a simple colored square with text
                    const canvas = document.createElement('canvas');
                    canvas.width = size;
                    canvas.height = size;
                    const ctx = canvas.getContext('2d');

                    // Blue background
                    ctx.fillStyle = '#2563eb';
                    ctx.fillRect(0, 0, size, size);

                    // White text
                    ctx.fillStyle = '#ffffff';
                    ctx.font = `bold ${size * 0.3}px Arial`;
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillText('RM', size/2, size/2);

                    downloadCanvas(canvas, `icon-${size}x${size}.png`);
                    completed++;

                    status.innerHTML = `<div class="status">Downloaded ${completed}/${sizes.length} basic icons...</div>`;

                    if (completed === sizes.length) {
                        status.innerHTML = `
                            <div class="status success">
                                ✅ All basic icons created!<br>
                                📁 Save them in the <code>icons/</code> folder<br>
                                🚀 Your PWA is ready to test!
                            </div>
                        `;
                    }
                }, index * 200);
            });
        }
        
        // Add CanvasRenderingContext2D.roundRect polyfill for older browsers
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
    </script>
</body>
</html>
