<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RestroManager - Restaurant Management System</title>
    
    <!-- PWA Meta Tags -->
    <meta name="description" content="Complete restaurant management solution for orders, menu, tasks, and analytics">
    <meta name="theme-color" content="#2563eb">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="RestroManager">
    <meta name="msapplication-TileColor" content="#2563eb">
    <meta name="msapplication-config" content="/browserconfig.xml">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="./manifest.json">

    <!-- PWA Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="./icons/icon-72x72.png">
    <link rel="icon" type="image/png" sizes="16x16" href="./icons/icon-72x72.png">
    <link rel="apple-touch-icon" sizes="180x180" href="./icons/icon-192x192.png">
    <link rel="mask-icon" href="./icons/icon-192x192.png" color="#2563eb">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest"></script>

    <!-- PWA Utils -->
    <script src="./pwa-utils.js"></script>
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        }
        .loading-spinner {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <div class="max-w-md mx-auto text-center">
        <div class="bg-white rounded-2xl shadow-xl p-8">
            <!-- Logo -->
            <div class="flex items-center justify-center mb-8">
                <div class="p-4 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg">
                    <i data-lucide="chef-hat" class="w-12 h-12 text-white"></i>
                </div>
            </div>
            
            <!-- Title -->
            <h1 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">
                RestroManager
            </h1>
            
            <!-- Subtitle -->
            <p class="text-slate-600 mb-8">
                Complete Restaurant Management Solution
            </p>
            
            <!-- Loading -->
            <div id="loading" class="flex items-center justify-center gap-3 mb-6">
                <div class="loading-spinner w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full"></div>
                <span class="text-slate-600">Loading your experience...</span>
            </div>
            
            <!-- Manual Navigation (hidden by default) -->
            <div id="manual-nav" class="hidden space-y-4">
                <p class="text-slate-600 mb-6">Choose your path:</p>

                <div class="space-y-3">
                    <a href="./login.html" class="block w-full bg-blue-600 text-white py-3 px-6 rounded-xl font-semibold hover:bg-blue-700 transition-colors">
                        Sign In
                    </a>

                    <a href="./signup.html" class="block w-full bg-green-600 text-white py-3 px-6 rounded-xl font-semibold hover:bg-green-700 transition-colors">
                        Create Account
                    </a>

                    <a href="./dashboard.html" class="block w-full bg-slate-100 text-slate-700 py-3 px-6 rounded-xl font-semibold hover:bg-slate-200 transition-colors">
                        Go to Dashboard
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Features -->
        <div class="mt-8 grid grid-cols-2 gap-4 text-center">
            <div class="bg-white/50 backdrop-blur-sm rounded-xl p-4">
                <i data-lucide="smartphone" class="w-8 h-8 text-blue-600 mx-auto mb-2"></i>
                <p class="text-sm font-medium text-slate-700">Mobile Ready</p>
            </div>
            <div class="bg-white/50 backdrop-blur-sm rounded-xl p-4">
                <i data-lucide="wifi-off" class="w-8 h-8 text-blue-600 mx-auto mb-2"></i>
                <p class="text-sm font-medium text-slate-700">Works Offline</p>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            lucide.createIcons();
            
            // Check user authentication status
            setTimeout(() => {
                const userData = JSON.parse(localStorage.getItem('restroManagerUser') || '{}');
                const loading = document.getElementById('loading');
                const manualNav = document.getElementById('manual-nav');

                if (userData.email && userData.isLoggedIn) {
                    // User is logged in, redirect to dashboard
                    window.location.href = './dashboard.html';
                } else if (userData.email && !userData.isLoggedIn) {
                    // User has account but not logged in, redirect to login
                    window.location.href = './login.html';
                } else {
                    // New user, show manual navigation
                    loading.classList.add('hidden');
                    manualNav.classList.remove('hidden');
                }

                // Fallback: show manual navigation after 3 seconds
                setTimeout(() => {
                    loading.classList.add('hidden');
                    manualNav.classList.remove('hidden');
                }, 3000);
            }, 1000);
        });
        
        // Handle PWA installation
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('PWA install prompt available');
        });
        
        // Handle app installed
        window.addEventListener('appinstalled', (e) => {
            console.log('PWA was installed');
        });
    </script>
</body>
</html>
