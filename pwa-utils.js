// PWA Utility Functions for RestroManager - Simplified for Reliable Installation

class PWAManager {
  constructor() {
    this.deferredPrompt = null;
    this.installButton = null;
    this.init();
  }

  init() {
    console.log('🚀 PWA Manager initializing...');
    this.registerServiceWorker();
    this.setupInstallPrompt();
    this.createInstallButton();
    this.checkForInstallPrompt();
  }

  // Check for install prompt availability
  checkForInstallPrompt() {
    // Wait a bit for the beforeinstallprompt event
    setTimeout(() => {
      if (!this.deferredPrompt) {
        console.log('💡 No install prompt detected. This might be because:');
        console.log('   - App is already installed');
        console.log('   - <PERSON><PERSON><PERSON> doesn\'t support PWA installation');
        console.log('   - PWA criteria not fully met');

        // Show button anyway for testing
        this.showInstallButton();
      }
    }, 2000);
  }

  // Register service worker
  async registerServiceWorker() {
    if ('serviceWorker' in navigator && window.location.protocol !== 'file:') {
      try {
        const registration = await navigator.serviceWorker.register('./sw.js', {
          scope: './'
        });
        console.log('✅ Service Worker registered successfully');
        return registration;
      } catch (error) {
        console.warn('⚠️ Service Worker registration failed:', error.message);
        // Continue without service worker
      }
    } else if (window.location.protocol === 'file:') {
      console.warn('⚠️ Service Worker not supported on file:// protocol');
    }
  }

  // Setup install prompt handling
  setupInstallPrompt() {
    window.addEventListener('beforeinstallprompt', (e) => {
      console.log('🎯 Install prompt available!');

      // Prevent the mini-infobar from appearing
      e.preventDefault();

      // Save the event for later use
      this.deferredPrompt = e;

      // Show install button
      this.showInstallButton();
    });

    // Handle app installed event
    window.addEventListener('appinstalled', (e) => {
      console.log('🎉 PWA was installed successfully!');
      this.hideInstallButton();
      this.showInstallSuccess();
    });
  }

  // Create install button
  createInstallButton() {
    // Remove existing button if any
    const existingBtn = document.getElementById('pwa-install-btn');
    if (existingBtn) {
      existingBtn.remove();
    }

    const installBtn = document.createElement('button');
    installBtn.id = 'pwa-install-btn';
    installBtn.className = 'install-btn';
    installBtn.innerHTML = `
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
        <polyline points="7,10 12,15 17,10"/>
        <line x1="12" y1="15" x2="12" y2="3"/>
      </svg>
      <span>Install App</span>
    `;

    installBtn.addEventListener('click', () => this.promptInstall());

    // Add styles
    const style = document.createElement('style');
    style.textContent = `
      .install-btn {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #3b82f6, #2563eb);
        color: white;
        border: none;
        border-radius: 12px;
        font-weight: 600;
        font-size: 14px;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
        transition: all 0.3s ease;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      .install-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(37, 99, 235, 0.5);
      }
      .install-btn:active {
        transform: translateY(0);
      }
      @media (max-width: 768px) {
        .install-btn {
          bottom: 20px;
          right: 20px;
          left: 20px;
          justify-content: center;
        }
      }
    `;

    if (!document.getElementById('pwa-install-styles')) {
      style.id = 'pwa-install-styles';
      document.head.appendChild(style);
    }

    this.installButton = installBtn;
    return installBtn;
  }

  // Show install button
  showInstallButton() {
    if (!this.installButton) {
      this.installButton = this.createInstallButton();
    }

    // Add to page if not already added
    if (!document.getElementById('pwa-install-btn')) {
      document.body.appendChild(this.installButton);
      console.log('📱 Install button shown');
    }
  }

  // Hide install button
  hideInstallButton() {
    const btn = document.getElementById('pwa-install-btn');
    if (btn) {
      btn.style.opacity = '0';
      btn.style.transform = 'translateY(-10px)';
      setTimeout(() => {
        btn.remove();
      }, 300);
      console.log('📱 Install button hidden');
    }
  }

  // Prompt install
  async promptInstall() {
    console.log('🚀 Install button clicked');

    // Show confirmation dialog
    const confirmed = confirm(
      'Install RestroManager?\n\n' +
      'This will add the app to your desktop/home screen for quick access. ' +
      'You can use it like a native app, even when offline.\n\n' +
      'Click OK to install, or Cancel to dismiss.'
    );

    if (!confirmed) {
      console.log('❌ User cancelled installation');
      return;
    }

    console.log('✅ User confirmed installation');

    if (this.deferredPrompt) {
      try {
        // Show the install prompt
        console.log('📱 Showing native install prompt...');
        this.deferredPrompt.prompt();

        // Wait for the user to respond to the prompt
        const { outcome } = await this.deferredPrompt.userChoice;

        console.log(`📊 User response: ${outcome}`);

        if (outcome === 'accepted') {
          console.log('🎉 User accepted the install prompt');
          this.showInstallSuccess();
        } else {
          console.log('😔 User dismissed the install prompt');
        }

        // Clear the deferredPrompt
        this.deferredPrompt = null;

      } catch (error) {
        console.error('❌ Error during installation:', error);
        this.showInstallError();
      }
    } else {
      // Fallback for browsers that don't support beforeinstallprompt
      console.log('💡 Showing manual install instructions');
      this.showManualInstallInstructions();
    }
  }

  // Show install success message
  showInstallSuccess() {
    this.showToast('🎉 App installed successfully!', 'success');
  }

  // Show install error
  showInstallError() {
    this.showToast('❌ Installation failed. Please try again.', 'error');
  }

  // Show manual install instructions
  showManualInstallInstructions() {
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const isAndroid = /Android/.test(navigator.userAgent);

    let instructions = 'To install this app:\n\n';

    if (isIOS) {
      instructions += '📱 On iOS:\n1. Tap the Share button (⬆️)\n2. Select "Add to Home Screen"\n3. Tap "Add"';
    } else if (isAndroid) {
      instructions += '📱 On Android:\n1. Tap the menu (⋮)\n2. Select "Add to Home screen"\n3. Tap "Add"';
    } else {
      instructions += '💻 On Desktop:\n1. Look for install icon in address bar\n2. Or use browser menu > "Install RestroManager"\n3. Follow the prompts';
    }

    alert(instructions);
  }

  // Show toast notification
  showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;

    const style = document.createElement('style');
    style.textContent = `
      .toast {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        padding: 16px 24px;
        border-radius: 12px;
        color: white;
        font-weight: 600;
        font-size: 14px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        transform: translateX(100%);
        transition: transform 0.3s ease;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      .toast-success { background: linear-gradient(135deg, #10b981, #059669); }
      .toast-error { background: linear-gradient(135deg, #ef4444, #dc2626); }
      .toast-info { background: linear-gradient(135deg, #3b82f6, #2563eb); }
    `;

    if (!document.getElementById('toast-styles')) {
      style.id = 'toast-styles';
      document.head.appendChild(style);
    }

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
      toast.style.transform = 'translateX(0)';
    }, 100);

    // Auto remove
    setTimeout(() => {
      toast.style.transform = 'translateX(100%)';
      setTimeout(() => {
        toast.remove();
      }, 300);
    }, 3000);
  }



}

// Initialize PWA Manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  console.log('🚀 Initializing PWA Manager...');
  window.pwaManager = new PWAManager();

  // Show install button after a short delay
  setTimeout(() => {
    window.pwaManager.showInstallButton();
  }, 1000);
});

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PWAManager;
}
