@echo off
echo.
echo ========================================
echo   RestroManager PWA Quick Start
echo ========================================
echo.

REM Create icons directory if it doesn't exist
if not exist "icons" (
    echo 📁 Creating icons directory...
    mkdir icons
    echo ✅ Icons directory created
) else (
    echo ✅ Icons directory already exists
)

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo.
    echo ❌ Node.js is not installed
    echo 💡 Please install Node.js from https://nodejs.org
    echo    OR use Python server: python -m http.server 8000
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js found
echo.

REM Check if icons exist
set icon_count=0
for %%f in (icons\*.png) do set /a icon_count+=1

if %icon_count% LSS 8 (
    echo ⚠️  Missing PWA icons! ^(%icon_count%/8 found^)
    echo.
    echo 🎨 To create icons:
    echo    1. Start the server first
    echo    2. Open http://localhost:8000/create-placeholder-icons.html
    echo    3. Click "Create Basic Icons (Faster)"
    echo    4. Save all downloaded files in the icons/ folder
    echo.
) else (
    echo ✅ PWA icons found ^(%icon_count%/8^)
)

echo 🚀 Starting PWA server...
echo.
echo 📱 Open these URLs in your browser:
echo    Main App: http://localhost:8000
echo    Dashboard: http://localhost:8000/dashboard.html
echo    Icon Creator: http://localhost:8000/create-placeholder-icons.html
echo.
echo 💡 The install button will work when accessed via HTTP server
echo 🔧 Press Ctrl+C to stop the server
echo.

node server.js 8000

echo.
echo 🛑 Server stopped
pause
