#!/usr/bin/env python3
"""
Simple HTTP Server for PWA Testing
Run this script to serve the RestroManager PWA locally with proper HTTPS and headers.
"""

import http.server
import socketserver
import ssl
import os
import mimetypes
from urllib.parse import urlparse

class P<PERSON>HTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Add PWA-specific headers
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        
        # Security headers
        self.send_header('X-Content-Type-Options', 'nosniff')
        self.send_header('X-Frame-Options', 'DENY')
        self.send_header('X-XSS-Protection', '1; mode=block')
        
        # CORS headers for development
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        
        super().end_headers()
    
    def do_GET(self):
        # Handle service worker with proper MIME type
        if self.path == '/sw.js':
            self.send_response(200)
            self.send_header('Content-Type', 'application/javascript')
            self.send_header('Service-Worker-Allowed', '/')
            self.end_headers()
            
            try:
                with open('sw.js', 'rb') as f:
                    self.wfile.write(f.read())
            except FileNotFoundError:
                self.send_error(404)
            return
        
        # Handle manifest.json with proper MIME type
        if self.path == '/manifest.json':
            self.send_response(200)
            self.send_header('Content-Type', 'application/manifest+json')
            self.end_headers()
            
            try:
                with open('manifest.json', 'rb') as f:
                    self.wfile.write(f.read())
            except FileNotFoundError:
                self.send_error(404)
            return
        
        # Handle root path
        if self.path == '/':
            self.path = '/index.html'
        
        # Serve static files
        super().do_GET()
    
    def log_message(self, format, *args):
        # Custom logging
        print(f"[PWA Server] {format % args}")

def run_server(port=8000, use_https=False):
    """Run the PWA development server"""
    
    handler = PWAHTTPRequestHandler
    
    with socketserver.TCPServer(("", port), handler) as httpd:
        if use_https:
            # Create self-signed certificate for HTTPS (required for PWA features)
            context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
            try:
                context.load_cert_chain('server.crt', 'server.key')
                httpd.socket = context.wrap_socket(httpd.socket, server_side=True)
                protocol = "https"
            except FileNotFoundError:
                print("SSL certificates not found. Generating self-signed certificate...")
                generate_ssl_cert()
                context.load_cert_chain('server.crt', 'server.key')
                httpd.socket = context.wrap_socket(httpd.socket, server_side=True)
                protocol = "https"
        else:
            protocol = "http"
        
        print(f"\n🚀 RestroManager PWA Server Starting...")
        print(f"📱 Server running at {protocol}://localhost:{port}")
        print(f"🌐 Access your PWA at {protocol}://localhost:{port}")
        print(f"📋 Dashboard: {protocol}://localhost:{port}/dashboard.html")
        print(f"✍️  Signup: {protocol}://localhost:{port}/signup.html")
        print(f"\n💡 For full PWA features, use HTTPS (add --https flag)")
        print(f"🔧 Press Ctrl+C to stop the server\n")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Server stopped by user")
            httpd.shutdown()

def generate_ssl_cert():
    """Generate self-signed SSL certificate for HTTPS testing"""
    try:
        import subprocess
        
        # Generate private key
        subprocess.run([
            'openssl', 'genrsa', '-out', 'server.key', '2048'
        ], check=True, capture_output=True)
        
        # Generate certificate
        subprocess.run([
            'openssl', 'req', '-new', '-x509', '-key', 'server.key',
            '-out', 'server.crt', '-days', '365', '-subj',
            '/C=US/ST=State/L=City/O=RestroManager/CN=localhost'
        ], check=True, capture_output=True)
        
        print("✅ SSL certificate generated successfully")
        
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Failed to generate SSL certificate")
        print("💡 Install OpenSSL or run without HTTPS")
        exit(1)

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='RestroManager PWA Development Server')
    parser.add_argument('--port', '-p', type=int, default=8000, help='Port to run server on (default: 8000)')
    parser.add_argument('--https', action='store_true', help='Use HTTPS (required for full PWA features)')
    
    args = parser.parse_args()
    
    # Change to script directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    run_server(args.port, args.https)
