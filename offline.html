<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - RestroManager</title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#2563eb">
    <meta name="apple-mobile-web-app-capable" content="yes">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest"></script>
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <div class="max-w-md mx-auto text-center">
        <div class="bg-white rounded-2xl shadow-xl p-8">
            <!-- Logo -->
            <div class="flex items-center justify-center mb-6">
                <div class="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg">
                    <i data-lucide="chef-hat" class="w-8 h-8 text-white"></i>
                </div>
            </div>
            
            <!-- Offline Icon -->
            <div class="w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <i data-lucide="wifi-off" class="w-10 h-10 text-orange-600"></i>
            </div>
            
            <!-- Title -->
            <h1 class="text-2xl font-bold text-slate-800 mb-3">You're Offline</h1>
            
            <!-- Description -->
            <p class="text-slate-600 mb-6">
                It looks like you've lost your internet connection. Don't worry, you can still access some features of RestroManager while offline.
            </p>
            
            <!-- Available Features -->
            <div class="bg-slate-50 rounded-xl p-4 mb-6">
                <h3 class="font-semibold text-slate-700 mb-3">Available Offline:</h3>
                <ul class="text-sm text-slate-600 space-y-2">
                    <li class="flex items-center gap-2">
                        <i data-lucide="check" class="w-4 h-4 text-green-600"></i>
                        View cached dashboard data
                    </li>
                    <li class="flex items-center gap-2">
                        <i data-lucide="check" class="w-4 h-4 text-green-600"></i>
                        Browse menu items
                    </li>
                    <li class="flex items-center gap-2">
                        <i data-lucide="check" class="w-4 h-4 text-green-600"></i>
                        Manage tasks locally
                    </li>
                    <li class="flex items-center gap-2">
                        <i data-lucide="x" class="w-4 h-4 text-red-500"></i>
                        Sync new data
                    </li>
                </ul>
            </div>
            
            <!-- Actions -->
            <div class="space-y-3">
                <button onclick="window.location.reload()" class="w-full bg-blue-600 text-white py-3 px-6 rounded-xl font-semibold hover:bg-blue-700 transition-colors">
                    Try Again
                </button>
                
                <button onclick="goToDashboard()" class="w-full bg-slate-100 text-slate-700 py-3 px-6 rounded-xl font-semibold hover:bg-slate-200 transition-colors">
                    Go to Dashboard
                </button>
            </div>
            
            <!-- Connection Status -->
            <div id="connection-status" class="mt-6 p-3 rounded-lg hidden">
                <div class="flex items-center justify-center gap-2">
                    <i data-lucide="wifi" class="w-5 h-5 text-green-600"></i>
                    <span class="text-green-700 font-medium">Back Online!</span>
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <p class="text-slate-500 text-sm mt-6">
            RestroManager works offline to keep your business running smoothly.
        </p>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            lucide.createIcons();
            
            // Monitor connection status
            const connectionStatus = document.getElementById('connection-status');
            
            function updateConnectionStatus() {
                if (navigator.onLine) {
                    connectionStatus.classList.remove('hidden');
                    connectionStatus.className = 'mt-6 p-3 rounded-lg bg-green-100';
                    setTimeout(() => {
                        window.location.href = '/dashboard.html';
                    }, 2000);
                } else {
                    connectionStatus.classList.add('hidden');
                }
            }
            
            // Listen for connection changes
            window.addEventListener('online', updateConnectionStatus);
            window.addEventListener('offline', updateConnectionStatus);
            
            // Initial check
            updateConnectionStatus();
        });
        
        function goToDashboard() {
            window.location.href = '/dashboard.html';
        }
        
        // Auto-refresh when back online
        window.addEventListener('online', () => {
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        });
    </script>
</body>
</html>
