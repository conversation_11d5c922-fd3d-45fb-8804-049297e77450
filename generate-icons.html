<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generate PWA Icons</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .icon-item {
            text-align: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .icon-item img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
        }
        .download-btn {
            background: #2563eb;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .download-btn:hover {
            background: #1d4ed8;
        }
    </style>
</head>
<body>
    <h1>RestroManager PWA Icons Generator</h1>
    <p>Click the buttons below to download the required PWA icons for RestroManager.</p>
    
    <div class="icon-grid">
        <div class="icon-item">
            <h3>72x72</h3>
            <canvas id="canvas-72" width="72" height="72"></canvas>
            <br>
            <button class="download-btn" onclick="downloadCanvasIcon(72)">Download</button>
        </div>

        <div class="icon-item">
            <h3>96x96</h3>
            <canvas id="canvas-96" width="96" height="96"></canvas>
            <br>
            <button class="download-btn" onclick="downloadCanvasIcon(96)">Download</button>
        </div>

        <div class="icon-item">
            <h3>128x128</h3>
            <canvas id="canvas-128" width="128" height="128"></canvas>
            <br>
            <button class="download-btn" onclick="downloadCanvasIcon(128)">Download</button>
        </div>

        <div class="icon-item">
            <h3>144x144</h3>
            <canvas id="canvas-144" width="144" height="144"></canvas>
            <br>
            <button class="download-btn" onclick="downloadCanvasIcon(144)">Download</button>
        </div>

        <div class="icon-item">
            <h3>152x152</h3>
            <canvas id="canvas-152" width="152" height="152"></canvas>
            <br>
            <button class="download-btn" onclick="downloadCanvasIcon(152)">Download</button>
        </div>

        <div class="icon-item">
            <h3>192x192</h3>
            <canvas id="canvas-192" width="192" height="192"></canvas>
            <br>
            <button class="download-btn" onclick="downloadCanvasIcon(192)">Download</button>
        </div>

        <div class="icon-item">
            <h3>384x384</h3>
            <canvas id="canvas-384" width="384" height="384"></canvas>
            <br>
            <button class="download-btn" onclick="downloadCanvasIcon(384)">Download</button>
        </div>

        <div class="icon-item">
            <h3>512x512</h3>
            <canvas id="canvas-512" width="512" height="512"></canvas>
            <br>
            <button class="download-btn" onclick="downloadCanvasIcon(512)">Download</button>
        </div>
    </div>
    
    <div style="margin-top: 40px; padding: 20px; background: #f0f9ff; border-radius: 8px;">
        <h3>Instructions:</h3>
        <ol>
            <li>Download all the icons above by clicking the download buttons</li>
            <li>Save them in the <code>icons/</code> folder with the naming format: <code>icon-{size}x{size}.png</code></li>
            <li>For example: <code>icon-192x192.png</code>, <code>icon-512x512.png</code>, etc.</li>
            <li>The PWA will automatically use these icons for different display contexts</li>
        </ol>
        
        <p><strong>Note:</strong> These are placeholder icons. For production, you should create custom icons with your restaurant's branding.</p>
    </div>

    <script>
        // Generate custom RestroManager icons with chef hat
        function generateIcon(size) {
            const canvas = document.getElementById(`canvas-${size}`);
            const ctx = canvas.getContext('2d');

            // Set canvas size
            canvas.width = size;
            canvas.height = size;

            // Clear canvas
            ctx.clearRect(0, 0, size, size);

            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#3b82f6');
            gradient.addColorStop(1, '#2563eb');

            // Draw rounded rectangle background
            const radius = size * 0.15;
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, radius);
            ctx.fill();

            // Draw chef hat (simplified)
            ctx.fillStyle = '#ffffff';
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = size * 0.02;

            const centerX = size / 2;
            const centerY = size / 2;
            const hatSize = size * 0.4;

            // Hat base (band)
            ctx.fillRect(centerX - hatSize * 0.6, centerY + hatSize * 0.3, hatSize * 1.2, hatSize * 0.2);

            // Hat top (puffy part)
            ctx.beginPath();
            ctx.ellipse(centerX, centerY - hatSize * 0.1, hatSize * 0.6, hatSize * 0.5, 0, 0, 2 * Math.PI);
            ctx.fill();

            // Hat details (small circles for texture)
            for (let i = 0; i < 3; i++) {
                ctx.beginPath();
                ctx.arc(centerX - hatSize * 0.3 + i * hatSize * 0.3, centerY - hatSize * 0.1, size * 0.02, 0, 2 * Math.PI);
                ctx.fillStyle = '#e2e8f0';
                ctx.fill();
            }

            // Add "RM" text below hat
            ctx.fillStyle = '#ffffff';
            ctx.font = `bold ${size * 0.15}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('RM', centerX, centerY + hatSize * 0.7);
        }

        // Generate all icons on page load
        document.addEventListener('DOMContentLoaded', () => {
            const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
            sizes.forEach(size => generateIcon(size));
        });

        // Download canvas as PNG
        function downloadCanvasIcon(size) {
            const canvas = document.getElementById(`canvas-${size}`);

            canvas.toBlob((blob) => {
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = `icon-${size}x${size}.png`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(link.href);

                console.log(`Downloaded icon-${size}x${size}.png`);
            }, 'image/png');
        }

        // Download all icons at once
        function downloadAllIcons() {
            const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
            sizes.forEach((size, index) => {
                setTimeout(() => downloadCanvasIcon(size), index * 500);
            });
        }

        // Add download all button
        const downloadAllBtn = document.createElement('button');
        downloadAllBtn.textContent = 'Download All RestroManager Icons';
        downloadAllBtn.className = 'download-btn';
        downloadAllBtn.style.fontSize = '16px';
        downloadAllBtn.style.padding = '12px 24px';
        downloadAllBtn.style.marginTop = '20px';
        downloadAllBtn.onclick = downloadAllIcons;

        document.querySelector('h1').after(downloadAllBtn);

        // Add regenerate button
        const regenerateBtn = document.createElement('button');
        regenerateBtn.textContent = 'Regenerate Icons';
        regenerateBtn.className = 'download-btn';
        regenerateBtn.style.fontSize = '14px';
        regenerateBtn.style.padding = '8px 16px';
        regenerateBtn.style.marginTop = '10px';
        regenerateBtn.style.marginLeft = '10px';
        regenerateBtn.onclick = () => {
            const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
            sizes.forEach(size => generateIcon(size));
        };

        downloadAllBtn.after(regenerateBtn);
    </script>
</body>
</html>
